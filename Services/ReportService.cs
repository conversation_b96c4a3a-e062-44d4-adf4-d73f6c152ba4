using ApartmanYonetimSistemi.Models;
using OfficeOpenXml;
using iTextSharp.text;
using iTextSharp.text.pdf;
using System.IO;
using System.Threading.Tasks;

namespace ApartmanYonetimSistemi.Services
{
    public class ReportService
    {
        private readonly FirebaseService _firebaseService;

        public ReportService()
        {
            _firebaseService = new FirebaseService();
            // EPPlus lisans ayarı
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
        }

        #region Excel Reports
        public async Task<byte[]> GeneratePaymentReportExcelAsync(string siteId, DateTime startDate, DateTime endDate)
        {
            var payments = await _firebaseService.GetPaymentsBySiteAsync(siteId);
            var filteredPayments = payments.Where(p => p.CreatedDate >= startDate && p.CreatedDate <= endDate).ToList();

            using var package = new ExcelPackage();
            var worksheet = package.Workbook.Worksheets.Add("Ödeme Raporu");

            // Başlıklar
            worksheet.Cells[1, 1].Value = "Daire No";
            worksheet.Cells[1, 2].Value = "Kiracı Adı";
            worksheet.Cells[1, 3].Value = "Ödeme Tipi";
            worksheet.Cells[1, 4].Value = "Tutar";
            worksheet.Cells[1, 5].Value = "Vade Tarihi";
            worksheet.Cells[1, 6].Value = "Ödeme Tarihi";
            worksheet.Cells[1, 7].Value = "Durum";
            worksheet.Cells[1, 8].Value = "Ödeme Yöntemi";

            // Başlık stilini ayarla
            using (var range = worksheet.Cells[1, 1, 1, 8])
            {
                range.Style.Font.Bold = true;
                range.Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                range.Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
            }

            // Veri satırları
            int row = 2;
            foreach (var payment in filteredPayments)
            {
                var flat = await _firebaseService.GetFlatAsync(payment.FlatId);
                var tenant = await _firebaseService.GetTenantAsync(payment.TenantId);

                worksheet.Cells[row, 1].Value = flat?.FlatNo ?? "";
                worksheet.Cells[row, 2].Value = tenant?.FullName ?? "";
                worksheet.Cells[row, 3].Value = payment.Type;
                worksheet.Cells[row, 4].Value = payment.Amount;
                worksheet.Cells[row, 5].Value = payment.DueDate.ToString("dd.MM.yyyy");
                worksheet.Cells[row, 6].Value = payment.PaidDate?.ToString("dd.MM.yyyy") ?? "";
                worksheet.Cells[row, 7].Value = payment.Status;
                worksheet.Cells[row, 8].Value = payment.PaymentMethod;

                row++;
            }

            // Sütun genişliklerini ayarla
            worksheet.Cells.AutoFitColumns();

            return package.GetAsByteArray();
        }

        public async Task<byte[]> GenerateTenantReportExcelAsync(string siteId)
        {
            var tenants = await _firebaseService.GetTenantsBySiteAsync(siteId);

            using var package = new ExcelPackage();
            var worksheet = package.Workbook.Worksheets.Add("Kiracı Raporu");

            // Başlıklar
            worksheet.Cells[1, 1].Value = "Daire No";
            worksheet.Cells[1, 2].Value = "Ad Soyad";
            worksheet.Cells[1, 3].Value = "Telefon";
            worksheet.Cells[1, 4].Value = "E-posta";
            worksheet.Cells[1, 5].Value = "Giriş Tarihi";
            worksheet.Cells[1, 6].Value = "Çıkış Tarihi";
            worksheet.Cells[1, 7].Value = "TC Kimlik No";
            worksheet.Cells[1, 8].Value = "Acil Durum İletişim";

            // Başlık stilini ayarla
            using (var range = worksheet.Cells[1, 1, 1, 8])
            {
                range.Style.Font.Bold = true;
                range.Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                range.Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
            }

            // Veri satırları
            int row = 2;
            foreach (var tenant in tenants)
            {
                var flat = await _firebaseService.GetFlatAsync(tenant.FlatId);

                worksheet.Cells[row, 1].Value = flat?.FlatNo ?? "";
                worksheet.Cells[row, 2].Value = tenant.FullName;
                worksheet.Cells[row, 3].Value = tenant.Phone;
                worksheet.Cells[row, 4].Value = tenant.Email;
                worksheet.Cells[row, 5].Value = tenant.EntryDate.ToString("dd.MM.yyyy");
                worksheet.Cells[row, 6].Value = tenant.ExitDate?.ToString("dd.MM.yyyy") ?? "";
                worksheet.Cells[row, 7].Value = tenant.IdentityNumber;
                worksheet.Cells[row, 8].Value = $"{tenant.EmergencyContact} - {tenant.EmergencyPhone}";

                row++;
            }

            worksheet.Cells.AutoFitColumns();
            return package.GetAsByteArray();
        }

        public async Task<byte[]> GenerateDebtorsReportExcelAsync(string siteId, DateTime endDate)
        {
            var payments = await _firebaseService.GetPaymentsBySiteAsync(siteId);
            var unpaidPayments = payments.Where(p => p.Status != PaymentStatus.Paid && p.DueDate <= endDate).ToList();

            using var package = new ExcelPackage();
            var worksheet = package.Workbook.Worksheets.Add("Borçlular Raporu");

            // Başlıklar
            worksheet.Cells[1, 1].Value = "Daire No";
            worksheet.Cells[1, 2].Value = "Kiracı Adı";
            worksheet.Cells[1, 3].Value = "Borç Türü";
            worksheet.Cells[1, 4].Value = "Borç Tutarı";
            worksheet.Cells[1, 5].Value = "Vade Tarihi";
            worksheet.Cells[1, 6].Value = "Geciken Gün";

            // Başlık stilini ayarla
            using (var range = worksheet.Cells[1, 1, 1, 6])
            {
                range.Style.Font.Bold = true;
                range.Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                range.Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.IndianRed);
                range.Style.Font.Color.SetColor(System.Drawing.Color.White);
            }

            // Veri satırları
            int row = 2;
            foreach (var payment in unpaidPayments)
            {
                var flat = await _firebaseService.GetFlatAsync(payment.FlatId);
                var tenant = await _firebaseService.GetTenantAsync(payment.TenantId);

                worksheet.Cells[row, 1].Value = flat?.FlatNo ?? "N/A";
                worksheet.Cells[row, 2].Value = tenant?.FullName ?? "N/A";
                worksheet.Cells[row, 3].Value = payment.Type;
                worksheet.Cells[row, 4].Value = payment.Amount;
                worksheet.Cells[row, 4].Style.Numberformat.Format = "₺#,##0.00";
                worksheet.Cells[row, 5].Value = payment.DueDate;
                worksheet.Cells[row, 5].Style.Numberformat.Format = "dd.MM.yyyy";
                worksheet.Cells[row, 6].Value = (endDate - payment.DueDate).Days;
                
                row++;
            }

            worksheet.Cells.AutoFitColumns();
            return await package.GetAsByteArrayAsync();
        }
        #endregion

        #region PDF Reports
        public async Task<byte[]> GeneratePaymentReportPdfAsync(string siteId, DateTime startDate, DateTime endDate)
        {
            var payments = await _firebaseService.GetPaymentsBySiteAsync(siteId);
            var filteredPayments = payments.Where(p => p.CreatedDate >= startDate && p.CreatedDate <= endDate).ToList();
            var site = await _firebaseService.GetSiteAsync(siteId);

            using var memoryStream = new MemoryStream();
            var document = new Document(PageSize.A4.Rotate(), 25, 25, 30, 30);
            var writer = PdfWriter.GetInstance(document, memoryStream);

            document.Open();

            // Başlık
            var titleFont = FontFactory.GetFont(FontFactory.HELVETICA_BOLD, 16);
            var title = new Paragraph($"ÖDEME RAPORU\n{site?.SiteName ?? ""}\n{startDate:dd.MM.yyyy} - {endDate:dd.MM.yyyy}", titleFont);
            title.Alignment = Element.ALIGN_CENTER;
            title.SpacingAfter = 20;
            document.Add(title);

            // Tablo
            var table = new PdfPTable(8);
            table.WidthPercentage = 100;
            table.SetWidths(new float[] { 10, 20, 15, 12, 12, 12, 10, 15 });

            // Başlık satırı
            var headerFont = FontFactory.GetFont(FontFactory.HELVETICA_BOLD, 10);
            table.AddCell(new PdfPCell(new Phrase("Daire No", headerFont)) { BackgroundColor = BaseColor.LightGray });
            table.AddCell(new PdfPCell(new Phrase("Kiracı Adı", headerFont)) { BackgroundColor = BaseColor.LightGray });
            table.AddCell(new PdfPCell(new Phrase("Ödeme Tipi", headerFont)) { BackgroundColor = BaseColor.LightGray });
            table.AddCell(new PdfPCell(new Phrase("Tutar", headerFont)) { BackgroundColor = BaseColor.LightGray });
            table.AddCell(new PdfPCell(new Phrase("Vade Tarihi", headerFont)) { BackgroundColor = BaseColor.LightGray });
            table.AddCell(new PdfPCell(new Phrase("Ödeme Tarihi", headerFont)) { BackgroundColor = BaseColor.LightGray });
            table.AddCell(new PdfPCell(new Phrase("Durum", headerFont)) { BackgroundColor = BaseColor.LightGray });
            table.AddCell(new PdfPCell(new Phrase("Ödeme Yöntemi", headerFont)) { BackgroundColor = BaseColor.LightGray });

            // Veri satırları
            var cellFont = FontFactory.GetFont(FontFactory.HELVETICA, 9);
            foreach (var payment in filteredPayments)
            {
                var flat = await _firebaseService.GetFlatAsync(payment.FlatId);
                var tenant = await _firebaseService.GetTenantAsync(payment.TenantId);

                table.AddCell(new PdfPCell(new Phrase(flat?.FlatNo ?? "", cellFont)));
                table.AddCell(new PdfPCell(new Phrase(tenant?.FullName ?? "", cellFont)));
                table.AddCell(new PdfPCell(new Phrase(payment.Type, cellFont)));
                table.AddCell(new PdfPCell(new Phrase(payment.Amount.ToString("C"), cellFont)));
                table.AddCell(new PdfPCell(new Phrase(payment.DueDate.ToString("dd.MM.yyyy"), cellFont)));
                table.AddCell(new PdfPCell(new Phrase(payment.PaidDate?.ToString("dd.MM.yyyy") ?? "", cellFont)));
                table.AddCell(new PdfPCell(new Phrase(payment.Status, cellFont)));
                table.AddCell(new PdfPCell(new Phrase(payment.PaymentMethod, cellFont)));
            }

            document.Add(table);

            // Özet bilgiler
            var totalAmount = filteredPayments.Sum(p => p.Amount);
            var paidAmount = filteredPayments.Where(p => p.Status == PaymentStatus.Paid).Sum(p => p.Amount);
            var unpaidAmount = totalAmount - paidAmount;

            var summaryParagraph = new Paragraph($"\n\nÖZET BİLGİLER:\nToplam Tutar: {totalAmount:C}\nÖdenen Tutar: {paidAmount:C}\nÖdenmemiş Tutar: {unpaidAmount:C}", headerFont);
            document.Add(summaryParagraph);

            document.Close();
            return memoryStream.ToArray();
        }
        #endregion

        #region Summary Reports
        public async Task<PaymentSummary> GetPaymentSummaryAsync(string siteId, DateTime startDate, DateTime endDate)
        {
            var payments = await _firebaseService.GetPaymentsBySiteAsync(siteId);
            var filteredPayments = payments.Where(p => p.CreatedDate >= startDate && p.CreatedDate <= endDate).ToList();

            return new PaymentSummary
            {
                SiteId = siteId,
                StartDate = startDate,
                EndDate = endDate,
                TotalPayments = filteredPayments.Count,
                TotalAmount = filteredPayments.Sum(p => p.Amount),
                PaidPayments = filteredPayments.Count(p => p.Status == PaymentStatus.Paid),
                PaidAmount = filteredPayments.Where(p => p.Status == PaymentStatus.Paid).Sum(p => p.Amount),
                UnpaidPayments = filteredPayments.Count(p => p.Status != PaymentStatus.Paid),
                UnpaidAmount = filteredPayments.Where(p => p.Status != PaymentStatus.Paid).Sum(p => p.Amount),
                OverduePayments = filteredPayments.Count(p => p.Status == PaymentStatus.Overdue),
                OverdueAmount = filteredPayments.Where(p => p.Status == PaymentStatus.Overdue).Sum(p => p.Amount)
            };
        }

        public async Task<List<FlatSummary>> GetFlatSummariesAsync(string siteId)
        {
            var flats = await _firebaseService.GetFlatsBySiteAsync(siteId);
            var summaries = new List<FlatSummary>();

            foreach (var flat in flats)
            {
                var tenant = await _firebaseService.GetTenantByFlatAsync(flat.Id);
                var payments = await _firebaseService.GetPaymentsByFlatAsync(flat.Id);

                summaries.Add(new FlatSummary
                {
                    FlatId = flat.Id,
                    FlatNo = flat.FlatNo,
                    IsOccupied = flat.IsOccupied,
                    TenantName = tenant?.FullName ?? "",
                    TenantPhone = tenant?.Phone ?? "",
                    RentAmount = flat.RentAmount,
                    DuesAmount = flat.Dues,
                    TotalUnpaidAmount = payments.Where(p => p.Status != PaymentStatus.Paid).Sum(p => p.Amount),
                    LastPaymentDate = payments.Where(p => p.Status == PaymentStatus.Paid).Max(p => p.PaidDate)
                });
            }

            return summaries;
        }
        #endregion
    }

    public class PaymentSummary
    {
        public string SiteId { get; set; } = string.Empty;
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public int TotalPayments { get; set; }
        public decimal TotalAmount { get; set; }
        public int PaidPayments { get; set; }
        public decimal PaidAmount { get; set; }
        public int UnpaidPayments { get; set; }
        public decimal UnpaidAmount { get; set; }
        public int OverduePayments { get; set; }
        public decimal OverdueAmount { get; set; }
    }

    public class FlatSummary
    {
        public string FlatId { get; set; } = string.Empty;
        public string FlatNo { get; set; } = string.Empty;
        public bool IsOccupied { get; set; }
        public string TenantName { get; set; } = string.Empty;
        public string TenantPhone { get; set; } = string.Empty;
        public decimal RentAmount { get; set; }
        public decimal DuesAmount { get; set; }
        public decimal TotalUnpaidAmount { get; set; }
        public DateTime? LastPaymentDate { get; set; }
    }
}