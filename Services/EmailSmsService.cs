using MailKit.Net.Smtp;
using MailKit.Security;
using MimeKit;
using System.Threading.Tasks;

namespace ApartmanYonetimSistemi.Services
{
    public class EmailSmsService
    {
        private readonly string _smtpServer;
        private readonly int _smtpPort;
        private readonly string _smtpUsername;
        private readonly string _smtpPassword;
        private readonly string _fromEmail;
        private readonly string _fromName;

        public EmailSmsService()
        {
            // SMTP ayarları - appsettings.json'dan okunmalı
            _smtpServer = "smtp.gmail.com"; // Gmail SMTP
            _smtpPort = 587;
            _smtpUsername = "<EMAIL>"; // Gerçek email ile değiştirilmeli
            _smtpPassword = "your-app-password"; // Gmail App Password ile değiştirilmeli
            _fromEmail = "<EMAIL>";
            _fromName = "Apartman Yönet<PERSON> Si<PERSON>";
        }

        public async Task<(bool Success, string Message)> SendEmailAsync(string toEmail, string toName, string subject, string body, bool isHtml = true)
        {
            try
            {
                var message = new MimeMessage();
                message.From.Add(new MailboxAddress(_fromName, _fromEmail));
                message.To.Add(new MailboxAddress(toName, toEmail));
                message.Subject = subject;

                var bodyBuilder = new BodyBuilder();
                if (isHtml)
                {
                    bodyBuilder.HtmlBody = body;
                }
                else
                {
                    bodyBuilder.TextBody = body;
                }
                message.Body = bodyBuilder.ToMessageBody();

                using var client = new SmtpClient();
                await client.ConnectAsync(_smtpServer, _smtpPort, SecureSocketOptions.StartTls);
                await client.AuthenticateAsync(_smtpUsername, _smtpPassword);
                await client.SendAsync(message);
                await client.DisconnectAsync(true);

                return (true, "E-posta başarıyla gönderildi");
            }
            catch (Exception ex)
            {
                return (false, $"E-posta gönderme hatası: {ex.Message}");
            }
        }

        public async Task<(bool Success, string Message)> SendPaymentReminderEmailAsync(string tenantEmail, string tenantName, string flatNo, decimal amount, string paymentType, DateTime dueDate)
        {
            string subject = $"Ödeme Hatırlatması - {paymentType}";

            string body = $@"
                <html>
                <body style='font-family: Arial, sans-serif;'>
                    <h2 style='color: #2c3e50;'>Ödeme Hatırlatması</h2>
                    <p>Sayın <strong>{tenantName}</strong>,</p>
                    <p><strong>{flatNo}</strong> numaralı daireniz için aşağıdaki ödemenizin vadesi yaklaşmaktadır:</p>

                    <div style='background-color: #f8f9fa; padding: 15px; border-left: 4px solid #007bff; margin: 20px 0;'>
                        <p><strong>Ödeme Tipi:</strong> {paymentType}</p>
                        <p><strong>Tutar:</strong> {amount:C}</p>
                        <p><strong>Vade Tarihi:</strong> {dueDate:dd.MM.yyyy}</p>
                    </div>

                    <p>Lütfen ödemenizi zamanında yapınız.</p>
                    <p>Teşekkürler,<br/>Apartman Yönetimi</p>
                </body>
                </html>";

            return await SendEmailAsync(tenantEmail, tenantName, subject, body, true);
        }

        public async Task<(bool Success, string Message)> SendOverduePaymentEmailAsync(string tenantEmail, string tenantName, string flatNo, decimal amount, string paymentType, DateTime dueDate, int daysOverdue)
        {
            string subject = $"Geciken Ödeme Bildirimi - {paymentType}";

            string body = $@"
                <html>
                <body style='font-family: Arial, sans-serif;'>
                    <h2 style='color: #dc3545;'>Geciken Ödeme Bildirimi</h2>
                    <p>Sayın <strong>{tenantName}</strong>,</p>
                    <p><strong>{flatNo}</strong> numaralı daireniz için aşağıdaki ödemeniz <strong>{daysOverdue} gün</strong> gecikmiştir:</p>

                    <div style='background-color: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 20px 0;'>
                        <p><strong>Ödeme Tipi:</strong> {paymentType}</p>
                        <p><strong>Tutar:</strong> {amount:C}</p>
                        <p><strong>Vade Tarihi:</strong> {dueDate:dd.MM.yyyy}</p>
                        <p><strong>Gecikme Süresi:</strong> {daysOverdue} gün</p>
                    </div>

                    <p style='color: #dc3545;'><strong>Lütfen ödemenizi en kısa sürede yapınız.</strong></p>
                    <p>Teşekkürler,<br/>Apartman Yönetimi</p>
                </body>
                </html>";

            return await SendEmailAsync(tenantEmail, tenantName, subject, body, true);
        }

        public async Task<(bool Success, string Message)> SendWelcomeEmailAsync(string tenantEmail, string tenantName, string flatNo, string siteName)
        {
            string subject = "Hoş Geldiniz - Apartman Yönetim Sistemi";

            string body = $@"
                <html>
                <body style='font-family: Arial, sans-serif;'>
                    <h2 style='color: #28a745;'>Hoş Geldiniz!</h2>
                    <p>Sayın <strong>{tenantName}</strong>,</p>
                    <p><strong>{siteName}</strong> - <strong>{flatNo}</strong> numaralı daireye hoş geldiniz!</p>

                    <div style='background-color: #d4edda; padding: 15px; border-left: 4px solid #28a745; margin: 20px 0;'>
                        <p>Apartman yönetim sistemi üzerinden:</p>
                        <ul>
                            <li>Ödeme durumlarınızı takip edebilirsiniz</li>
                            <li>Geçmiş ödemelerinizi görüntüleyebilirsiniz</li>
                            <li>Önemli duyuruları alabilirsiniz</li>
                        </ul>
                    </div>

                    <p>Herhangi bir sorunuz olduğunda bizimle iletişime geçebilirsiniz.</p>
                    <p>İyi günler,<br/>Apartman Yönetimi</p>
                </body>
                </html>";

            return await SendEmailAsync(tenantEmail, tenantName, subject, body, true);
        }

        // SMS fonksiyonları - Türkiye'de yaygın SMS API'leri için
        public async Task<(bool Success, string Message)> SendSmsAsync(string phoneNumber, string message)
        {
            try
            {
                // Bu kısım gerçek SMS API entegrasyonu için placeholder
                // Türkiye'de yaygın SMS sağlayıcıları: İletimerkezi, Netgsm, Mutlucell vb.

                // Örnek HTTP client kullanımı:
                /*
                using var client = new HttpClient();
                var content = new FormUrlEncodedContent(new[]
                {
                    new KeyValuePair<string, string>("username", "your-username"),
                    new KeyValuePair<string, string>("password", "your-password"),
                    new KeyValuePair<string, string>("phone", phoneNumber),
                    new KeyValuePair<string, string>("message", message)
                });

                var response = await client.PostAsync("https://api.sms-provider.com/send", content);
                var result = await response.Content.ReadAsStringAsync();

                return response.IsSuccessStatusCode ? (true, "SMS gönderildi") : (false, "SMS gönderilemedi");
                */

                // Şimdilik simüle edilmiş başarılı sonuç
                await Task.Delay(100); // API çağrısını simüle et
                return (true, "SMS başarıyla gönderildi (simülasyon)");
            }
            catch (Exception ex)
            {
                return (false, $"SMS gönderme hatası: {ex.Message}");
            }
        }

        public async Task<(bool Success, string Message)> SendPaymentReminderSmsAsync(string phoneNumber, string tenantName, string flatNo, decimal amount, DateTime dueDate)
        {
            string message = $"Sayın {tenantName}, {flatNo} no'lu daire için {amount:C} tutarındaki ödemenizin vadesi {dueDate:dd.MM.yyyy}. Lütfen zamanında ödeyiniz. Apartman Yönetimi";
            return await SendSmsAsync(phoneNumber, message);
        }

        public async Task<(bool Success, string Message)> SendOverduePaymentSmsAsync(string phoneNumber, string tenantName, string flatNo, decimal amount, int daysOverdue)
        {
            string message = $"Sayın {tenantName}, {flatNo} no'lu daire için {amount:C} tutarındaki ödemeniz {daysOverdue} gün gecikmiştir. Lütfen en kısa sürede ödeyiniz. Apartman Yönetimi";
            return await SendSmsAsync(phoneNumber, message);
        }
    }
}