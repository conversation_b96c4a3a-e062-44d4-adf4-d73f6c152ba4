using ApartmanYonetimSistemi.Models;
using System.Threading.Tasks;

namespace ApartmanYonetimSistemi.Services
{
    public class NotificationService
    {
        private readonly FirebaseService _firebaseService;
        private readonly EmailSmsService _emailSmsService;

        public NotificationService()
        {
            _firebaseService = new FirebaseService();
            _emailSmsService = new EmailSmsService();
        }

        public async Task SendPaymentRemindersAsync(string siteId, int daysAhead = 3)
        {
            try
            {
                // Yaklaşan <PERSON> al
                var upcomingPayments = await _firebaseService.GetUpcomingPaymentsAsync(siteId, daysAhead);

                foreach (var payment in upcomingPayments)
                {
                    // Kiracı bilgilerini al
                    var tenant = await _firebaseService.GetTenantAsync(payment.TenantId);
                    if (tenant == null) continue;

                    // Daire bilgilerini al
                    var flat = await _firebaseService.GetFlatAsync(payment.FlatId);
                    if (flat == null) continue;

                    // Email gönder
                    if (!string.IsNullOrEmpty(tenant.Email))
                    {
                        await _emailSmsService.SendPaymentReminderEmailAsync(
                            tenant.Email,
                            tenant.FullName,
                            flat.FlatNo,
                            payment.Amount,
                            payment.Type,
                            payment.DueDate
                        );
                    }

                    // SMS gönder
                    if (!string.IsNullOrEmpty(tenant.Phone))
                    {
                        await _emailSmsService.SendPaymentReminderSmsAsync(
                            tenant.Phone,
                            tenant.FullName,
                            flat.FlatNo,
                            payment.Amount,
                            payment.DueDate
                        );
                    }
                }
            }
            catch (Exception ex)
            {
                // Log error
                Console.WriteLine($"Payment reminder error: {ex.Message}");
            }
        }

        public async Task SendOverduePaymentNotificationsAsync(string siteId)
        {
            try
            {
                // Geciken ödemeleri al
                var overduePayments = await _firebaseService.GetOverduePaymentsAsync(siteId);

                foreach (var payment in overduePayments)
                {
                    // Kiracı bilgilerini al
                    var tenant = await _firebaseService.GetTenantAsync(payment.TenantId);
                    if (tenant == null) continue;

                    // Daire bilgilerini al
                    var flat = await _firebaseService.GetFlatAsync(payment.FlatId);
                    if (flat == null) continue;

                    int daysOverdue = (DateTime.Now - payment.DueDate).Days;

                    // Email gönder
                    if (!string.IsNullOrEmpty(tenant.Email))
                    {
                        await _emailSmsService.SendOverduePaymentEmailAsync(
                            tenant.Email,
                            tenant.FullName,
                            flat.FlatNo,
                            payment.Amount,
                            payment.Type,
                            payment.DueDate,
                            daysOverdue
                        );
                    }

                    // SMS gönder
                    if (!string.IsNullOrEmpty(tenant.Phone))
                    {
                        await _emailSmsService.SendOverduePaymentSmsAsync(
                            tenant.Phone,
                            tenant.FullName,
                            flat.FlatNo,
                            payment.Amount,
                            daysOverdue
                        );
                    }

                    // Ödeme durumunu "Overdue" olarak güncelle
                    if (payment.Status != PaymentStatus.Overdue)
                    {
                        payment.Status = PaymentStatus.Overdue;
                        await _firebaseService.UpdatePaymentAsync(payment);
                    }
                }
            }
            catch (Exception ex)
            {
                // Log error
                Console.WriteLine($"Overdue payment notification error: {ex.Message}");
            }
        }

        public async Task SendWelcomeNotificationAsync(string tenantId)
        {
            try
            {
                var tenant = await _firebaseService.GetTenantAsync(tenantId);
                if (tenant == null) return;

                var flat = await _firebaseService.GetFlatAsync(tenant.FlatId);
                if (flat == null) return;

                var apartment = await _firebaseService.GetApartmentAsync(flat.ApartmentId);
                if (apartment == null) return;

                var site = await _firebaseService.GetSiteAsync(flat.SiteId);
                if (site == null) return;

                string siteName = $"{site.SiteName} - {apartment.ApartmentName}";

                // Hoş geldin e-postası gönder
                if (!string.IsNullOrEmpty(tenant.Email))
                {
                    await _emailSmsService.SendWelcomeEmailAsync(
                        tenant.Email,
                        tenant.FullName,
                        flat.FlatNo,
                        siteName
                    );
                }
            }
            catch (Exception ex)
            {
                // Log error
                Console.WriteLine($"Welcome notification error: {ex.Message}");
            }
        }

        public async Task ProcessScheduledNotificationsAsync()
        {
            try
            {
                // Tüm aktif siteleri al
                var sites = await _firebaseService.GetAllSitesAsync();

                foreach (var site in sites)
                {
                    // Her site için ödeme hatırlatmaları gönder
                    await SendPaymentRemindersAsync(site.Id, 3); // 3 gün önceden hatırlat

                    // Geciken ödeme bildirimleri gönder
                    await SendOverduePaymentNotificationsAsync(site.Id);

                    // Kısa bir bekleme süresi
                    await Task.Delay(1000);
                }
            }
            catch (Exception ex)
            {
                // Log error
                Console.WriteLine($"Scheduled notifications error: {ex.Message}");
            }
        }

        public async Task<NotificationSummary> GetNotificationSummaryAsync(string siteId)
        {
            try
            {
                var upcomingPayments = await _firebaseService.GetUpcomingPaymentsAsync(siteId, 7);
                var overduePayments = await _firebaseService.GetOverduePaymentsAsync(siteId);

                return new NotificationSummary
                {
                    SiteId = siteId,
                    UpcomingPaymentsCount = upcomingPayments.Count,
                    OverduePaymentsCount = overduePayments.Count,
                    TotalUpcomingAmount = upcomingPayments.Sum(p => p.Amount),
                    TotalOverdueAmount = overduePayments.Sum(p => p.Amount),
                    LastChecked = DateTime.Now
                };
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Notification summary error: {ex.Message}");
                return new NotificationSummary { SiteId = siteId, LastChecked = DateTime.Now };
            }
        }
    }

    public class NotificationSummary
    {
        public string SiteId { get; set; } = string.Empty;
        public int UpcomingPaymentsCount { get; set; }
        public int OverduePaymentsCount { get; set; }
        public decimal TotalUpcomingAmount { get; set; }
        public decimal TotalOverdueAmount { get; set; }
        public DateTime LastChecked { get; set; }
    }
}