using Google.Cloud.Firestore;
using ApartmanYonetimSistemi.Models;
using System.Threading.Tasks;
using System.IO;

namespace ApartmanYonetimSistemi.Services
{
    public class FirebaseService
    {
        private readonly FirestoreDb? _firestoreDb;
        private readonly bool _isConnected;

        // Mock data storage
        private readonly List<Site> _mockSites = new();
        private readonly List<Apartment> _mockApartments = new();
        private readonly List<Flat> _mockFlats = new();
        private readonly List<Tenant> _mockTenants = new();
        private readonly List<User> _mockUsers = new();
        private readonly List<Payment> _mockPayments = new();
        private readonly List<PaymentHistory> _mockPaymentHistory = new();

        public FirebaseService()
        {
            try
            {
                // Proje ID ve JSON dosya yolu
                string projectId = "apartmanyonetimi-ab3d7";
                string jsonPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "apartmanyonetimi-ab3d7-firebase-adminsdk-fbsvc-cd63bef3c0.json");

                if (File.Exists(jsonPath))
                {
                    Environment.SetEnvironmentVariable("GOOGLE_APPLICATION_CREDENTIALS", jsonPath);
                    _firestoreDb = FirestoreDb.Create(projectId);
                    _isConnected = true;
                    System.Diagnostics.Debug.WriteLine("Firebase bağlantısı başarılı!");
                }
                else
                {
                    _isConnected = false;
                    System.Diagnostics.Debug.WriteLine($"Firebase JSON dosyası bulunamadı: {jsonPath}");
                    InitializeMockData();
                }
            }
            catch (Exception ex)
            {
                _isConnected = false;
                System.Diagnostics.Debug.WriteLine($"Firebase bağlantı hatası: {ex.Message}");
                InitializeMockData();
            }
        }

        private void InitializeMockData()
        {
            // Test kullanıcısı oluştur
            var testUser = new User
            {
                Id = "admin-test-id",
                Name = "Test Admin",
                Email = "<EMAIL>",
                Role = UserRoles.Admin,
                IsActive = true,
                CreatedDate = DateTime.Now.AddDays(-30),
                LastLoginDate = DateTime.Now
            };
            _mockUsers.Add(testUser);

            // Test verileri oluştur
            var testSite = new Site
            {
                Id = "test-site-1",
                SiteName = "Test Sitesi",
                Address = "Test Mahallesi, Test Sokak No:1",
                OwnerId = "admin-test-id",
                CreatedDate = DateTime.Now.AddDays(-30),
                IsActive = true
            };
            _mockSites.Add(testSite);

            var testApartment = new Apartment
            {
                Id = "test-apartment-1",
                ApartmentName = "A Blok",
                SiteId = testSite.Id,
                Address = testSite.Address,
                TotalFlats = 8,
                CreatedDate = DateTime.Now.AddDays(-30),
                IsActive = true
            };
            _mockApartments.Add(testApartment);

            // Test daireleri
            for (int i = 1; i <= 4; i++)
            {
                var flat = new Flat
                {
                    Id = $"test-flat-{i}",
                    FlatNo = $"A{i}",
                    ApartmentId = testApartment.Id,
                    SiteId = testSite.Id,
                    RentAmount = 5000 + (i * 500),
                    Dues = 300,
                    RentStatus = i <= 2 ? "Paid" : "Due",
                    DuesStatus = i <= 3 ? "Paid" : "Due",
                    IsOccupied = i <= 3,
                    CreatedDate = DateTime.Now.AddDays(-25),
                    IsActive = true
                };
                _mockFlats.Add(flat);

                if (i <= 3)
                {
                    var tenant = new Tenant
                    {
                        Id = $"test-tenant-{i}",
                        Name = $"Test{i}",
                        Surname = "Kiracı",
                        Phone = $"555-000-{i:D3}",
                        Email = $"test{i}@example.com",
                        FlatId = flat.Id,
                        ApartmentId = testApartment.Id,
                        SiteId = testSite.Id,
                        EntryDate = DateTime.Now.AddDays(-20),
                        IdentityNumber = $"1234567890{i}",
                        IsActive = true,
                        CreatedDate = DateTime.Now.AddDays(-20)
                    };
                    _mockTenants.Add(tenant);
                }
            }
        }

        #region Site Operations
        public async Task<string> AddSiteAsync(Site site)
        {
            await Task.Delay(100); // Simulate async operation

            if (_isConnected && _firestoreDb != null)
            {
                site.Id = Guid.NewGuid().ToString();
                site.CreatedDate = DateTime.Now;

                DocumentReference docRef = await _firestoreDb.Collection("Sites").AddAsync(site);
                site.Id = docRef.Id;

                await docRef.SetAsync(site);
                return site.Id;
            }
            else
            {
                // Mock implementation
                site.Id = Guid.NewGuid().ToString();
                site.CreatedDate = DateTime.Now;
                _mockSites.Add(site);
                return site.Id;
            }
        }

        public async Task<Site?> GetSiteAsync(string siteId)
        {
            await Task.Delay(50);

            if (_isConnected && _firestoreDb != null)
            {
                DocumentSnapshot snapshot = await _firestoreDb.Collection("Sites").Document(siteId).GetSnapshotAsync();
                return snapshot.Exists ? snapshot.ConvertTo<Site>() : null;
            }
            else
            {
                return _mockSites.FirstOrDefault(s => s.Id == siteId);
            }
        }

        public async Task<List<Site>> GetSitesByOwnerAsync(string ownerId)
        {
            await Task.Delay(100);

            if (_isConnected && _firestoreDb != null)
            {
                Query query = _firestoreDb.Collection("Sites")
                    .WhereEqualTo("ownerId", ownerId)
                    .WhereEqualTo("isActive", true);

                QuerySnapshot snapshot = await query.GetSnapshotAsync();
                return snapshot.Documents.Select(doc => doc.ConvertTo<Site>()).ToList();
            }
            else
            {
                return _mockSites.Where(s => s.OwnerId == ownerId && s.IsActive).ToList();
            }
        }

        public async Task<List<Site>> GetAllSitesAsync()
        {
            await Task.Delay(100);

            if (_isConnected && _firestoreDb != null)
            {
                Query query = _firestoreDb.Collection("Sites").WhereEqualTo("isActive", true);
                QuerySnapshot snapshot = await query.GetSnapshotAsync();
                return snapshot.Documents.Select(doc => doc.ConvertTo<Site>()).ToList();
            }
            else
            {
                return _mockSites.Where(s => s.IsActive).ToList();
            }
        }

        public async Task UpdateSiteAsync(Site site)
        {
            await Task.Delay(50);

            if (_isConnected && _firestoreDb != null)
            {
                await _firestoreDb.Collection("Sites").Document(site.Id).SetAsync(site, SetOptions.MergeAll);
            }
            else
            {
                var existingSite = _mockSites.FirstOrDefault(s => s.Id == site.Id);
                if (existingSite != null)
                {
                    var index = _mockSites.IndexOf(existingSite);
                    _mockSites[index] = site;
                }
            }
        }

        public async Task DeleteSiteAsync(string siteId)
        {
            var site = await GetSiteAsync(siteId);
            if (site != null)
            {
                site.IsActive = false;
                await UpdateSiteAsync(site);
            }
        }
        #endregion

        #region Apartment Operations
        public async Task<string> AddApartmentAsync(Apartment apartment)
        {
            await Task.Delay(100);

            if (_isConnected && _firestoreDb != null)
            {
                apartment.Id = Guid.NewGuid().ToString();
                apartment.CreatedDate = DateTime.Now;

                DocumentReference docRef = await _firestoreDb.Collection("Apartments").AddAsync(apartment);
                apartment.Id = docRef.Id;

                await docRef.SetAsync(apartment);
                return apartment.Id;
            }
            else
            {
                apartment.Id = Guid.NewGuid().ToString();
                apartment.CreatedDate = DateTime.Now;
                _mockApartments.Add(apartment);
                return apartment.Id;
            }
        }

        public async Task<Apartment?> GetApartmentAsync(string apartmentId)
        {
            await Task.Delay(50);

            if (_isConnected && _firestoreDb != null)
            {
                DocumentSnapshot snapshot = await _firestoreDb.Collection("Apartments").Document(apartmentId).GetSnapshotAsync();
                return snapshot.Exists ? snapshot.ConvertTo<Apartment>() : null;
            }
            else
            {
                return _mockApartments.FirstOrDefault(a => a.Id == apartmentId);
            }
        }

        public async Task<List<Apartment>> GetApartmentsBySiteAsync(string siteId)
        {
            await Task.Delay(100);

            if (_isConnected && _firestoreDb != null)
            {
                Query query = _firestoreDb.Collection("Apartments")
                    .WhereEqualTo("siteId", siteId)
                    .WhereEqualTo("isActive", true);

                QuerySnapshot snapshot = await query.GetSnapshotAsync();
                return snapshot.Documents.Select(doc => doc.ConvertTo<Apartment>()).ToList();
            }
            else
            {
                return _mockApartments.Where(a => a.SiteId == siteId && a.IsActive).ToList();
            }
        }

        public async Task UpdateApartmentAsync(Apartment apartment)
        {
            await Task.Delay(50);

            if (_isConnected && _firestoreDb != null)
            {
                await _firestoreDb.Collection("Apartments").Document(apartment.Id).SetAsync(apartment, SetOptions.MergeAll);
            }
            else
            {
                var existing = _mockApartments.FirstOrDefault(a => a.Id == apartment.Id);
                if (existing != null)
                {
                    var index = _mockApartments.IndexOf(existing);
                    _mockApartments[index] = apartment;
                }
            }
        }

        public async Task DeleteApartmentAsync(string apartmentId)
        {
            var apartment = await GetApartmentAsync(apartmentId);
            if (apartment != null)
            {
                apartment.IsActive = false;
                await UpdateApartmentAsync(apartment);
            }
        }
        #endregion

        #region Flat Operations
        public async Task<string> AddFlatAsync(Flat flat)
        {
            await Task.Delay(100);

            if (_isConnected && _firestoreDb != null)
            {
                flat.Id = Guid.NewGuid().ToString();
                flat.CreatedDate = DateTime.Now;

                DocumentReference docRef = await _firestoreDb.Collection("Flats").AddAsync(flat);
                flat.Id = docRef.Id;

                await docRef.SetAsync(flat);
                return flat.Id;
            }
            else
            {
                flat.Id = Guid.NewGuid().ToString();
                flat.CreatedDate = DateTime.Now;
                _mockFlats.Add(flat);
                return flat.Id;
            }
        }

        public async Task<Flat?> GetFlatAsync(string flatId)
        {
            await Task.Delay(50);

            if (_isConnected && _firestoreDb != null)
            {
                DocumentSnapshot snapshot = await _firestoreDb.Collection("Flats").Document(flatId).GetSnapshotAsync();
                return snapshot.Exists ? snapshot.ConvertTo<Flat>() : null;
            }
            else
            {
                return _mockFlats.FirstOrDefault(f => f.Id == flatId);
            }
        }

        public async Task<List<Flat>> GetFlatsByApartmentAsync(string apartmentId)
        {
            await Task.Delay(50);

            if (_isConnected && _firestoreDb != null)
            {
                Query query = _firestoreDb.Collection("Flats")
                    .WhereEqualTo("apartmentId", apartmentId)
                    .WhereEqualTo("isActive", true);

                QuerySnapshot snapshot = await query.GetSnapshotAsync();
                return snapshot.Documents.Select(doc => doc.ConvertTo<Flat>()).ToList();
            }
            else
            {
                return _mockFlats.Where(f => f.ApartmentId == apartmentId && f.IsActive).ToList();
            }
        }

        public async Task<List<Flat>> GetFlatsBySiteAsync(string siteId)
        {
            await Task.Delay(50);

            if (_isConnected && _firestoreDb != null)
            {
                Query query = _firestoreDb.Collection("Flats")
                    .WhereEqualTo("siteId", siteId)
                    .WhereEqualTo("isActive", true);

                QuerySnapshot snapshot = await query.GetSnapshotAsync();
                return snapshot.Documents.Select(doc => doc.ConvertTo<Flat>()).ToList();
            }
            else
            {
                return _mockFlats.Where(f => f.SiteId == siteId && f.IsActive).ToList();
            }
        }

        public async Task UpdateFlatAsync(Flat flat)
        {
            await Task.Delay(50);

            if (_isConnected && _firestoreDb != null)
            {
                await _firestoreDb.Collection("Flats").Document(flat.Id).SetAsync(flat, SetOptions.MergeAll);
            }
            else
            {
                var existingFlat = _mockFlats.FirstOrDefault(f => f.Id == flat.Id);
                if (existingFlat != null)
                {
                    var index = _mockFlats.IndexOf(existingFlat);
                    _mockFlats[index] = flat;
                }
            }
        }

        public async Task DeleteFlatAsync(string flatId)
        {
            await Task.Delay(50);

            if (_isConnected && _firestoreDb != null)
            {
                var flat = await GetFlatAsync(flatId);
                if (flat != null)
                {
                    flat.IsActive = false;
                    await UpdateFlatAsync(flat);
                }
            }
            else
            {
                var flat = _mockFlats.FirstOrDefault(f => f.Id == flatId);
                if (flat != null)
                {
                    flat.IsActive = false;
                }
            }
        }
        #endregion

        #region Tenant Operations
        public async Task<string> AddTenantAsync(Tenant tenant)
        {
            await Task.Delay(100);

            if (_isConnected && _firestoreDb != null)
            {
                tenant.Id = Guid.NewGuid().ToString();
                tenant.CreatedDate = DateTime.Now;

                DocumentReference docRef = await _firestoreDb.Collection("Tenants").AddAsync(tenant);
                tenant.Id = docRef.Id;

                await docRef.SetAsync(tenant);
                return tenant.Id;
            }
            else
            {
                tenant.Id = Guid.NewGuid().ToString();
                tenant.CreatedDate = DateTime.Now;
                _mockTenants.Add(tenant);
                return tenant.Id;
            }
        }

        public async Task<Tenant?> GetTenantAsync(string tenantId)
        {
            await Task.Delay(50);

            if (_isConnected && _firestoreDb != null)
            {
                DocumentSnapshot snapshot = await _firestoreDb.Collection("Tenants").Document(tenantId).GetSnapshotAsync();
                return snapshot.Exists ? snapshot.ConvertTo<Tenant>() : null;
            }
            else
            {
                return _mockTenants.FirstOrDefault(t => t.Id == tenantId);
            }
        }

        public async Task<Tenant?> GetTenantByFlatAsync(string flatId)
        {
            await Task.Delay(50);

            if (_isConnected && _firestoreDb != null)
            {
                Query query = _firestoreDb.Collection("Tenants")
                    .WhereEqualTo("flatId", flatId)
                    .WhereEqualTo("isActive", true)
                    .Limit(1);

                QuerySnapshot snapshot = await query.GetSnapshotAsync();
                return snapshot.Documents.FirstOrDefault()?.ConvertTo<Tenant>();
            }
            else
            {
                return _mockTenants.FirstOrDefault(t => t.FlatId == flatId && t.IsActive);
            }
        }

        public async Task<List<Tenant>> GetTenantsBySiteAsync(string siteId)
        {
            await Task.Delay(100);

            if (_isConnected && _firestoreDb != null)
            {
                Query query = _firestoreDb.Collection("Tenants")
                    .WhereEqualTo("siteId", siteId)
                    .WhereEqualTo("isActive", true);

                QuerySnapshot snapshot = await query.GetSnapshotAsync();
                return snapshot.Documents.Select(doc => doc.ConvertTo<Tenant>()).ToList();
            }
            else
            {
                return _mockTenants.Where(t => t.SiteId == siteId && t.IsActive).ToList();
            }
        }

        public async Task<List<Tenant>> GetTenantsByFlatAsync(string flatId)
        {
            await Task.Delay(100);

            if (_isConnected && _firestoreDb != null)
            {
                Query query = _firestoreDb.Collection("Tenants")
                    .WhereEqualTo("flatId", flatId)
                    .WhereEqualTo("isActive", true);

                QuerySnapshot snapshot = await query.GetSnapshotAsync();
                return snapshot.Documents.Select(doc => doc.ConvertTo<Tenant>()).ToList();
            }
            else
            {
                return _mockTenants.Where(t => t.FlatId == flatId && t.IsActive).ToList();
            }
        }

        public async Task UpdateTenantAsync(Tenant tenant)
        {
            await Task.Delay(50);

            if (_isConnected && _firestoreDb != null)
            {
                await _firestoreDb.Collection("Tenants").Document(tenant.Id).SetAsync(tenant, SetOptions.MergeAll);
            }
            else
            {
                var existingTenant = _mockTenants.FirstOrDefault(t => t.Id == tenant.Id);
                if (existingTenant != null)
                {
                    var index = _mockTenants.IndexOf(existingTenant);
                    _mockTenants[index] = tenant;
                }
            }
        }

        public async Task DeleteTenantAsync(string tenantId)
        {
            await Task.Delay(50);

            if (_isConnected && _firestoreDb != null)
            {
                var tenant = await GetTenantAsync(tenantId);
                if (tenant != null)
                {
                    tenant.IsActive = false;
                    tenant.ExitDate = DateTime.Now;
                    await UpdateTenantAsync(tenant);
                }
            }
            else
            {
                var tenant = _mockTenants.FirstOrDefault(t => t.Id == tenantId);
                if (tenant != null)
                {
                    tenant.IsActive = false;
                    tenant.ExitDate = DateTime.Now;
                }
            }
        }
        #endregion

        #region User Operations
        public async Task<string> AddUserAsync(User user)
        {
            await Task.Delay(100);

            if (_isConnected && _firestoreDb != null)
            {
                user.Id = Guid.NewGuid().ToString();
                user.CreatedDate = DateTime.Now;

                DocumentReference docRef = await _firestoreDb.Collection("Users").AddAsync(user);
                user.Id = docRef.Id;

                await docRef.SetAsync(user);
                return user.Id;
            }
            else
            {
                user.Id = Guid.NewGuid().ToString();
                user.CreatedDate = DateTime.Now;
                _mockUsers.Add(user);
                return user.Id;
            }
        }

        public async Task<User?> GetUserAsync(string userId)
        {
            await Task.Delay(50);

            if (_isConnected && _firestoreDb != null)
            {
                DocumentSnapshot snapshot = await _firestoreDb.Collection("Users").Document(userId).GetSnapshotAsync();
                return snapshot.Exists ? snapshot.ConvertTo<User>() : null;
            }
            else
            {
                return _mockUsers.FirstOrDefault(u => u.Id == userId);
            }
        }

        public async Task<User?> GetUserByEmailAsync(string email)
        {
            await Task.Delay(50);

            if (_isConnected && _firestoreDb != null)
            {
                Query query = _firestoreDb.Collection("Users")
                    .WhereEqualTo("email", email)
                    .WhereEqualTo("isActive", true)
                    .Limit(1);

                QuerySnapshot snapshot = await query.GetSnapshotAsync();
                return snapshot.Documents.FirstOrDefault()?.ConvertTo<User>();
            }
            else
            {
                return _mockUsers.FirstOrDefault(u => u.Email == email && u.IsActive);
            }
        }

        public async Task<User?> GetUserByFirebaseUidAsync(string firebaseUid)
        {
            await Task.Delay(50);

            if (_isConnected && _firestoreDb != null)
            {
                Query query = _firestoreDb.Collection("Users")
                    .WhereEqualTo("firebaseUid", firebaseUid)
                    .WhereEqualTo("isActive", true)
                    .Limit(1);

                QuerySnapshot snapshot = await query.GetSnapshotAsync();
                return snapshot.Documents.FirstOrDefault()?.ConvertTo<User>();
            }
            else
            {
                return _mockUsers.FirstOrDefault(u => u.FirebaseUid == firebaseUid && u.IsActive);
            }
        }

        public async Task UpdateUserAsync(User user)
        {
            await Task.Delay(50);

            if (_isConnected && _firestoreDb != null)
            {
                await _firestoreDb.Collection("Users").Document(user.Id).SetAsync(user, SetOptions.MergeAll);
            }
            else
            {
                var existing = _mockUsers.FirstOrDefault(u => u.Id == user.Id);
                if (existing != null)
                {
                    var index = _mockUsers.IndexOf(existing);
                    _mockUsers[index] = user;
                }
            }
        }

        public async Task UpdateUserLastLoginAsync(string userId)
        {
            try
            {
                var user = await GetUserAsync(userId);
                if (user != null)
                {
                    user.LastLoginDate = DateTime.Now;
                    await UpdateUserAsync(user);
                }
            }
            catch (Exception ex)
            {
                // Log the error but don't throw - this is not critical for login
                System.Diagnostics.Debug.WriteLine($"Failed to update user last login: {ex.Message}");
            }
        }

        public async Task DeleteUserAsync(string userId)
        {
            var user = await GetUserAsync(userId);
            if (user != null)
            {
                user.IsActive = false;
                await UpdateUserAsync(user);
            }
        }
        #endregion

        #region Payment Operations
        public async Task<string> AddPaymentAsync(Payment payment)
        {
            await Task.Delay(100);

            if (_isConnected && _firestoreDb != null)
            {
                payment.Id = Guid.NewGuid().ToString();
                payment.CreatedDate = DateTime.Now;

                DocumentReference docRef = await _firestoreDb.Collection("Payments").AddAsync(payment);
                payment.Id = docRef.Id;

                await docRef.SetAsync(payment);
                return payment.Id;
            }
            else
            {
                payment.Id = Guid.NewGuid().ToString();
                payment.CreatedDate = DateTime.Now;
                _mockPayments.Add(payment);
                return payment.Id;
            }
        }

        public async Task<Payment?> GetPaymentAsync(string paymentId)
        {
            await Task.Delay(50);

            if (_isConnected && _firestoreDb != null)
            {
                DocumentSnapshot snapshot = await _firestoreDb.Collection("Payments").Document(paymentId).GetSnapshotAsync();
                return snapshot.Exists ? snapshot.ConvertTo<Payment>() : null;
            }
            else
            {
                return _mockPayments.FirstOrDefault(p => p.Id == paymentId);
            }
        }

        public async Task<List<Payment>> GetPaymentsByFlatAsync(string flatId)
        {
            await Task.Delay(50);

            if (_isConnected && _firestoreDb != null)
            {
                Query query = _firestoreDb.Collection("Payments")
                    .WhereEqualTo("flatId", flatId)
                    .OrderByDescending("dueDate");

                QuerySnapshot snapshot = await query.GetSnapshotAsync();
                return snapshot.Documents.Select(doc => doc.ConvertTo<Payment>()).ToList();
            }
            else
            {
                return _mockPayments.Where(p => p.FlatId == flatId)
                    .OrderByDescending(p => p.DueDate)
                    .ToList();
            }
        }

        public async Task<List<Payment>> GetPaymentsBySiteAsync(string siteId)
        {
            await Task.Delay(50);

            if (_isConnected && _firestoreDb != null)
            {
                Query query = _firestoreDb.Collection("Payments")
                    .WhereEqualTo("siteId", siteId)
                    .OrderByDescending("dueDate");

                QuerySnapshot snapshot = await query.GetSnapshotAsync();
                return snapshot.Documents.Select(doc => doc.ConvertTo<Payment>()).ToList();
            }
            else
            {
                return _mockPayments.Where(p => p.SiteId == siteId)
                    .OrderByDescending(p => p.DueDate)
                    .ToList();
            }
        }

        public async Task<List<Payment>> GetOverduePaymentsAsync(string siteId)
        {
            await Task.Delay(50);

            if (_isConnected && _firestoreDb != null)
            {
                Query query = _firestoreDb.Collection("Payments")
                    .WhereEqualTo("siteId", siteId)
                    .WhereEqualTo("status", PaymentStatus.Due)
                    .WhereLessThan("dueDate", DateTime.Now);

                QuerySnapshot snapshot = await query.GetSnapshotAsync();
                return snapshot.Documents.Select(doc => doc.ConvertTo<Payment>()).ToList();
            }
            else
            {
                return _mockPayments.Where(p => p.SiteId == siteId && 
                                               p.Status == PaymentStatus.Due && 
                                               p.DueDate < DateTime.Now).ToList();
            }
        }

        public async Task<List<Payment>> GetUpcomingPaymentsAsync(string siteId, int daysAhead = 7)
        {
            await Task.Delay(50);

            if (_isConnected && _firestoreDb != null)
            {
                DateTime endDate = DateTime.Now.AddDays(daysAhead);
                Query query = _firestoreDb.Collection("Payments")
                    .WhereEqualTo("siteId", siteId)
                    .WhereEqualTo("status", PaymentStatus.Due)
                    .WhereGreaterThanOrEqualTo("dueDate", DateTime.Now)
                    .WhereLessThanOrEqualTo("dueDate", endDate);

                QuerySnapshot snapshot = await query.GetSnapshotAsync();
                return snapshot.Documents.Select(doc => doc.ConvertTo<Payment>()).ToList();
            }
            else
            {
                DateTime endDate = DateTime.Now.AddDays(daysAhead);
                return _mockPayments.Where(p => p.SiteId == siteId && 
                                               p.Status == PaymentStatus.Due && 
                                               p.DueDate >= DateTime.Now && 
                                               p.DueDate <= endDate).ToList();
            }
        }

        public async Task UpdatePaymentAsync(Payment payment)
        {
            await Task.Delay(50);

            if (_isConnected && _firestoreDb != null)
            {
                await _firestoreDb.Collection("Payments").Document(payment.Id).SetAsync(payment, SetOptions.MergeAll);
            }
            else
            {
                var existingPayment = _mockPayments.FirstOrDefault(p => p.Id == payment.Id);
                if (existingPayment != null)
                {
                    var index = _mockPayments.IndexOf(existingPayment);
                    _mockPayments[index] = payment;
                }
            }
        }

        public async Task MarkPaymentAsPaidAsync(string paymentId, string paymentMethod, string receiptNumber, string processedBy)
        {
            var payment = await GetPaymentAsync(paymentId);
            if (payment != null)
            {
                string previousStatus = payment.Status;
                payment.Status = PaymentStatus.Paid;
                payment.PaidDate = DateTime.Now;
                payment.PaymentMethod = paymentMethod;
                payment.ReceiptNumber = receiptNumber;

                await UpdatePaymentAsync(payment);

                // Payment history ekle
                var history = new PaymentHistory
                {
                    PaymentId = paymentId,
                    FlatId = payment.FlatId,
                    TenantId = payment.TenantId,
                    ApartmentId = payment.ApartmentId,
                    SiteId = payment.SiteId,
                    Amount = payment.Amount,
                    PreviousStatus = previousStatus,
                    NewStatus = PaymentStatus.Paid,
                    PaymentMethod = paymentMethod,
                    ReceiptNumber = receiptNumber,
                    ProcessedBy = processedBy
                };

                await AddPaymentHistoryAsync(history);
            }
        }
        #endregion

        #region PaymentHistory Operations
        public async Task<string> AddPaymentHistoryAsync(PaymentHistory history)
        {
            await Task.Delay(100);

            if (_isConnected && _firestoreDb != null)
            {
                history.Id = Guid.NewGuid().ToString();
                history.CreatedDate = DateTime.Now;

                DocumentReference docRef = await _firestoreDb.Collection("PaymentHistory").AddAsync(history);
                history.Id = docRef.Id;

                await docRef.SetAsync(history);
                return history.Id;
            }
            else
            {
                history.Id = Guid.NewGuid().ToString();
                history.CreatedDate = DateTime.Now;
                _mockPaymentHistory.Add(history);
                return history.Id;
            }
        }

        public async Task<List<PaymentHistory>> GetPaymentHistoryByPaymentAsync(string paymentId)
        {
            await Task.Delay(50);

            if (_isConnected && _firestoreDb != null)
            {
                Query query = _firestoreDb.Collection("PaymentHistory")
                    .WhereEqualTo("paymentId", paymentId)
                    .OrderByDescending("transactionDate");

                QuerySnapshot snapshot = await query.GetSnapshotAsync();
                return snapshot.Documents.Select(doc => doc.ConvertTo<PaymentHistory>()).ToList();
            }
            else
            {
                return _mockPaymentHistory.Where(h => h.PaymentId == paymentId)
                    .OrderByDescending(h => h.TransactionDate)
                    .ToList();
            }
        }

        public async Task<List<PaymentHistory>> GetPaymentHistoryBySiteAsync(string siteId, int limit = 100)
        {
            await Task.Delay(50);

            if (_isConnected && _firestoreDb != null)
            {
                Query query = _firestoreDb.Collection("PaymentHistory")
                    .WhereEqualTo("siteId", siteId)
                    .OrderByDescending("transactionDate")
                    .Limit(limit);

                QuerySnapshot snapshot = await query.GetSnapshotAsync();
                return snapshot.Documents.Select(doc => doc.ConvertTo<PaymentHistory>()).ToList();
            }
            else
            {
                return _mockPaymentHistory.Where(h => h.SiteId == siteId)
                    .OrderByDescending(h => h.TransactionDate)
                    .Take(limit)
                    .ToList();
            }
        }
        #endregion

        #region Real-time Listeners
        public void ListenToSiteChanges(string siteId, Action<Site> onSiteChanged)
        {
            if (_isConnected && _firestoreDb != null)
            {
                _firestoreDb.Collection("Sites").Document(siteId).Listen(snapshot =>
                {
                    if (snapshot.Exists)
                    {
                        var site = snapshot.ConvertTo<Site>();
                        onSiteChanged(site);
                    }
                });
            }
        }

        public void ListenToPaymentChanges(string siteId, Action<List<Payment>> onPaymentsChanged)
        {
            if (_isConnected && _firestoreDb != null)
            {
                _firestoreDb.Collection("Payments")
                    .WhereEqualTo("siteId", siteId)
                    .Listen(snapshot =>
                    {
                        var payments = snapshot.Documents.Select(doc => doc.ConvertTo<Payment>()).ToList();
                        onPaymentsChanged(payments);
                    });
            }
        }
        #endregion
    }
}