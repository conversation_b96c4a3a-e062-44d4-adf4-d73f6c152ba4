{"format": 1, "restore": {"C:\\Users\\<USER>\\apartman_yonetim_sistemi - Kopya-augment\\ApartmanYonetimSistemi.csproj": {}}, "projects": {"C:\\Users\\<USER>\\apartman_yonetim_sistemi - Kopya-augment\\ApartmanYonetimSistemi.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\apartman_yonet<PERSON>_siste<PERSON> - Kopya-augment\\ApartmanYonetimSistemi.csproj", "projectName": "ApartmanYonetimSistemi", "projectPath": "C:\\Users\\<USER>\\apartman_yonet<PERSON>_siste<PERSON> - Kopya-augment\\ApartmanYonetimSistemi.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\apartman_yonet<PERSON>_siste<PERSON> - Kopya-augment\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"EPPlus": {"target": "Package", "version": "[7.5.1, )"}, "FirebaseAuthentication.net": {"target": "Package", "version": "[4.1.0, )"}, "Google.Cloud.Firestore": {"target": "Package", "version": "[3.10.0, )"}, "MailKit": {"target": "Package", "version": "[4.8.0, )"}, "MaterialDesignThemes": {"target": "Package", "version": "[5.1.0, )"}, "Microsoft.Xaml.Behaviors.Wpf": {"target": "Package", "version": "[1.1.135, )"}, "iTextSharp.LGPLv2.Core": {"target": "Package", "version": "[3.4.22, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}