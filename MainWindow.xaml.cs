using System.Windows;
using ApartmanYonetimSistemi.Services;
using ApartmanYonetimSistemi.Views;

namespace ApartmanYonetimSistemi;

/// <summary>
/// Interaction logic for MainWindow.xaml
/// </summary>
public partial class MainWindow : Window
{
    private readonly AuthService _authService;

    public MainWindow()
    {
        InitializeComponent();
        _authService = new AuthService();
        PasswordBox.Password = "123456"; // Test için

        // Enter tuşu desteği
        EmailTextBox.KeyDown += (s, e) => { if (e.Key == System.Windows.Input.Key.Enter) LoginButton_Click(s, e); };
        PasswordBox.KeyDown += (s, e) => { if (e.Key == System.Windows.Input.Key.Enter) LoginButton_Click(s, e); };
        EmailTextBox.PreviewMouseWheel += (s, e) => e.Handled = true;
    }

    private async void LoginButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            string email = EmailTextBox.Text;
            string password = PasswordBox.Password;

            var (success, message, user) = await _authService.LoginAsync(email, password);

            if (success && user != null)
            {
                await App.MainViewModel.InitializeAsync(user);

                var mainView = new Window
                {
                    Title = "Apartman Yönetim Sistemi - Ana Panel",
                    Content = new MainView(),
                    WindowState = WindowState.Maximized
                };
                
                mainView.Show();
                this.Close();
            }
            else
            {
                MessageBox.Show(message, "Giriş Başarısız", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Bir hata oluştu: {ex.Message}", "Hata", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }
}
