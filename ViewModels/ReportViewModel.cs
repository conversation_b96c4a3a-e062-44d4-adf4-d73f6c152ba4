using ApartmanYonetimSistemi.Helpers;
using ApartmanYonetimSistemi.Services;
using System.Collections.ObjectModel;
using System;
using System.IO;
using Microsoft.Win32;
using System.Windows;

namespace ApartmanYonetimSistemi.ViewModels
{
    public class ReportViewModel : BaseViewModel
    {
        private readonly ReportService _reportService;
        private bool _isLoading;
        private string? _selectedReportType;
        private DateTime? _startDate = DateTime.Now.AddMonths(-1);
        private DateTime? _endDate = DateTime.Now;
        private string _statusMessage = string.Empty;

        public ObservableCollection<string> ReportTypes { get; }

        public string? SelectedReportType
        {
            get => _selectedReportType;
            set => SetProperty(ref _selectedReportType, value);
        }

        public DateTime? StartDate
        {
            get => _startDate;
            set => SetProperty(ref _startDate, value);
        }

        public DateTime? EndDate
        {
            get => _endDate;
            set => SetProperty(ref _endDate, value);
        }

        public bool IsLoading
        {
            get => _isLoading;
            set => SetProperty(ref _isLoading, value);
        }

        public string StatusMessage
        {
            get => _statusMessage;
            set => SetProperty(ref _statusMessage, value);
        }

        public RelayCommand GenerateReportCommand { get; }

        public ReportViewModel()
        {
            _reportService = new ReportService();
            ReportTypes = new ObservableCollection<string>
            {
                "Borçlular Raporu",
                "Gelir Raporu",
                "Doluluk Raporu"
            };
            
            GenerateReportCommand = new RelayCommand(async _ => await GenerateReportAsync(), _ => CanGenerateReport());

            PropertyChanged += (s, e) => {
                if(e.PropertyName == nameof(SelectedReportType)) {
                    GenerateReportCommand.RaiseCanExecuteChanged();
                }
            };
            
            // MainViewModel'deki site seçimi değiştiğinde CanExecute durumunu güncelle
            App.MainViewModel.PropertyChanged += (s, e) =>
            {
                if (e.PropertyName == nameof(App.MainViewModel.SelectedSite))
                {
                    GenerateReportCommand.RaiseCanExecuteChanged();
                }
            };
        }

        private bool CanGenerateReport()
        {
            return !string.IsNullOrEmpty(SelectedReportType) && 
                   !IsLoading && 
                   App.MainViewModel.SelectedSite != null;
        }

        private async Task GenerateReportAsync()
        {
            var selectedSite = App.MainViewModel.SelectedSite;

            if (selectedSite == null)
            {
                StatusMessage = "Lütfen bir rapor türü ve site seçin.";
                return;
            }

            IsLoading = true;
            StatusMessage = "Rapor oluşturuluyor, lütfen bekleyin...";
            byte[]? reportData = null;

            try
            {
                string siteId = selectedSite.Id;
                string reportFileName = "report.xlsx";

                switch (SelectedReportType)
                {
                    case "Borçlular Raporu":
                        reportData = await _reportService.GenerateDebtorsReportExcelAsync(siteId, EndDate ?? DateTime.Now);
                        reportFileName = $"Borclular_Raporu_{DateTime.Now:yyyyMMdd}.xlsx";
                        break;
                    // Diğer rapor türleri için case'ler buraya eklenecek
                    default:
                        StatusMessage = "Seçilen rapor türü için oluşturma metodu bulunamadı.";
                        IsLoading = false;
                        return;
                }
                
                SaveReportToFile(reportData, reportFileName);
            }
            catch(Exception ex)
            {
                StatusMessage = $"Rapor oluşturulurken bir hata oluştu: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        private void SaveReportToFile(byte[] reportData, string fileName)
        {
            if (reportData == null || reportData.Length == 0)
            {
                StatusMessage = "Oluşturulacak veri bulunamadı.";
                return;
            }

            SaveFileDialog saveFileDialog = new SaveFileDialog
            {
                FileName = fileName,
                Filter = "Excel Dosyası (*.xlsx)|*.xlsx|Tüm Dosyalar (*.*)|*.*",
                Title = "Raporu Kaydet"
            };

            if (saveFileDialog.ShowDialog() == true)
            {
                try
                {
                    File.WriteAllBytes(saveFileDialog.FileName, reportData);
                    StatusMessage = $"Rapor başarıyla kaydedildi: {saveFileDialog.FileName}";
                }
                catch (Exception ex)
                {
                    StatusMessage = $"Dosya kaydedilirken bir hata oluştu: {ex.Message}";
                }
            }
            else
            {
                StatusMessage = "Rapor kaydetme işlemi iptal edildi.";
            }
        }
    }
} 