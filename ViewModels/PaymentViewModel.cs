using ApartmanYonetimSistemi.Models;
using ApartmanYonetimSistemi.Helpers;
using ApartmanYonetimSistemi.Services;
using System.Collections.ObjectModel;
using System.ComponentModel.DataAnnotations;

namespace ApartmanYonetimSistemi.ViewModels
{
    public class PaymentViewModel : BaseViewModel
    {
        private ObservableCollection<Payment> _payments = new();
        private ObservableCollection<Payment> _overduePayments = new();
        private ObservableCollection<Payment> _upcomingPayments = new();
        private ObservableCollection<Flat> _flats = new();
        private Payment? _selectedPayment;
        private Payment _currentPayment = new();
        private bool _isLoading = false;
        private bool _isEditing = false;
        private string _errorMessage = string.Empty;
        private string _successMessage = string.Empty;
        private string _selectedSiteId = string.Empty;
        private string _paymentFilter = "All"; // All, Paid, Due, Overdue
        private readonly FirebaseService _firebaseService;
        private readonly AuthService _authService;
        private readonly NotificationService _notificationService;

        public ObservableCollection<Payment> Payments
        {
            get => _payments;
            set => SetProperty(ref _payments, value);
        }

        public ObservableCollection<Payment> OverduePayments
        {
            get => _overduePayments;
            set => SetProperty(ref _overduePayments, value);
        }

        public ObservableCollection<Payment> UpcomingPayments
        {
            get => _upcomingPayments;
            set => SetProperty(ref _upcomingPayments, value);
        }

        public ObservableCollection<Flat> Flats
        {
            get => _flats;
            set => SetProperty(ref _flats, value);
        }

        public Payment? SelectedPayment
        {
            get => _selectedPayment;
            set
            {
                if (SetProperty(ref _selectedPayment, value))
                {
                    if (value != null)
                    {
                        CurrentPayment = new Payment
                        {
                            Id = value.Id,
                            FlatId = value.FlatId,
                            TenantId = value.TenantId,
                            ApartmentId = value.ApartmentId,
                            SiteId = value.SiteId,
                            Amount = value.Amount,
                            Type = value.Type,
                            DueDate = value.DueDate,
                            PaidDate = value.PaidDate,
                            Status = value.Status,
                            Description = value.Description,
                            PaymentMethod = value.PaymentMethod,
                            ReceiptNumber = value.ReceiptNumber,
                            CreatedDate = value.CreatedDate,
                            CreatedBy = value.CreatedBy
                        };
                        IsEditing = true;
                    }
                }
            }
        }

        public Payment CurrentPayment
        {
            get => _currentPayment;
            set => SetProperty(ref _currentPayment, value);
        }

        public string SelectedSiteId
        {
            get => _selectedSiteId;
            set
            {
                if (SetProperty(ref _selectedSiteId, value))
                {
                    CurrentPayment.SiteId = value;
                    _ = LoadPaymentsAsync();
                    _ = LoadFlatsAsync();
                }
            }
        }

        public string PaymentFilter
        {
            get => _paymentFilter;
            set
            {
                if (SetProperty(ref _paymentFilter, value))
                {
                    _ = LoadPaymentsAsync();
                }
            }
        }

        public bool IsLoading
        {
            get => _isLoading;
            set => SetProperty(ref _isLoading, value);
        }

        public bool IsEditing
        {
            get => _isEditing;
            set => SetProperty(ref _isEditing, value);
        }

        public string ErrorMessage
        {
            get => _errorMessage;
            set => SetProperty(ref _errorMessage, value);
        }

        public string SuccessMessage
        {
            get => _successMessage;
            set => SetProperty(ref _successMessage, value);
        }

        // Commands
        public RelayCommand LoadPaymentsCommand { get; }
        public RelayCommand AddPaymentCommand { get; }
        public RelayCommand EditPaymentCommand { get; }
        public RelayCommand SavePaymentCommand { get; }
        public RelayCommand DeletePaymentCommand { get; }
        public RelayCommand CancelEditCommand { get; }
        public RelayCommand NewPaymentCommand { get; }
        public RelayCommand MarkAsPaidCommand { get; }
        public RelayCommand SendReminderCommand { get; }
        public RelayCommand GenerateMonthlyPaymentsCommand { get; }

        public PaymentViewModel()
        {
            _firebaseService = new FirebaseService();
            _authService = new AuthService();
            _notificationService = new NotificationService();

            LoadPaymentsCommand = new RelayCommand(async _ => await LoadPaymentsAsync());
            AddPaymentCommand = new RelayCommand(async _ => await SavePaymentAsync(), _ => CanSavePayment() && !IsEditing);
            EditPaymentCommand = new RelayCommand(_ => EditPayment(), _ => SelectedPayment != null);
            SavePaymentCommand = new RelayCommand(async _ => await SavePaymentAsync(), _ => CanSavePayment());
            DeletePaymentCommand = new RelayCommand(async _ => await DeletePaymentAsync(), _ => CanDeletePayment());
            CancelEditCommand = new RelayCommand(_ => CancelEdit());
            NewPaymentCommand = new RelayCommand(_ => NewPayment());
            MarkAsPaidCommand = new RelayCommand(async _ => await MarkAsPaidAsync(), _ => CanMarkAsPaid());
            SendReminderCommand = new RelayCommand(async _ => await SendReminderAsync(), _ => SelectedPayment != null);
            GenerateMonthlyPaymentsCommand = new RelayCommand(async _ => await GenerateMonthlyPaymentsAsync(), _ => !string.IsNullOrEmpty(SelectedSiteId));
        }

        private async Task LoadPaymentsAsync()
        {
            if (string.IsNullOrEmpty(SelectedSiteId)) return;

            try
            {
                IsLoading = true;
                Payments.Clear();
                OverduePayments.Clear();
                UpcomingPayments.Clear();
                ClearMessages();

                var allPayments = await _firebaseService.GetPaymentsBySiteAsync(SelectedSiteId);

                // Filter payments based on selected filter
                var filteredPayments = PaymentFilter switch
                {
                    "Paid" => allPayments.Where(p => p.Status == PaymentStatus.Paid),
                    "Due" => allPayments.Where(p => p.Status == PaymentStatus.Due),
                    "Overdue" => allPayments.Where(p => p.Status == PaymentStatus.Overdue || p.IsOverdue),
                    _ => allPayments
                };

                foreach (var payment in filteredPayments.OrderByDescending(p => p.DueDate))
                {
                    Payments.Add(payment);
                }

                // Load overdue and upcoming payments for dashboard
                var overduePayments = await _firebaseService.GetOverduePaymentsAsync(SelectedSiteId);
                foreach (var payment in overduePayments)
                {
                    OverduePayments.Add(payment);
                }

                var upcomingPayments = await _firebaseService.GetUpcomingPaymentsAsync(SelectedSiteId, 7);
                foreach (var payment in upcomingPayments)
                {
                    UpcomingPayments.Add(payment);
                }
            }
            catch (Exception ex)
            {
                ErrorMessage = $"Ödemeler yüklenirken hata oluştu: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task LoadFlatsAsync()
        {
            if (string.IsNullOrEmpty(SelectedSiteId)) return;

            try
            {
                Flats.Clear();
                var flats = await _firebaseService.GetFlatsBySiteAsync(SelectedSiteId);
                foreach (var flat in flats.Where(f => f.IsOccupied))
                {
                    Flats.Add(flat);
                }
            }
            catch (Exception ex)
            {
                ErrorMessage = $"Daireler yüklenirken hata oluştu: {ex.Message}";
            }
        }

        private async Task SavePaymentAsync()
        {
            try
            {
                IsLoading = true;
                ClearMessages();

                // Validation
                var validationResults = new List<ValidationResult>();
                var validationContext = new ValidationContext(CurrentPayment);
                if (!Validator.TryValidateObject(CurrentPayment, validationContext, validationResults, true))
                {
                    ErrorMessage = string.Join("\n", validationResults.Select(r => r.ErrorMessage));
                    return;
                }

                if (string.IsNullOrEmpty(CurrentPayment.SiteId))
                {
                    ErrorMessage = "Lütfen bir site seçin";
                    return;
                }

                if (string.IsNullOrEmpty(CurrentPayment.FlatId))
                {
                    ErrorMessage = "Lütfen bir daire seçin";
                    return;
                }

                // Get tenant info
                var tenant = await _firebaseService.GetTenantByFlatAsync(CurrentPayment.FlatId);
                if (tenant != null)
                {
                    CurrentPayment.TenantId = tenant.Id;
                    CurrentPayment.ApartmentId = tenant.ApartmentId;
                }

                var currentUser = _authService.CurrentUser;
                CurrentPayment.CreatedBy = currentUser?.Id ?? "system";

                if (IsEditing)
                {
                    // Güncelleme
                    await _firebaseService.UpdatePaymentAsync(CurrentPayment);
                    SuccessMessage = "Ödeme başarıyla güncellendi";

                    // Listedeki ödemeyi güncelle
                    var existingPayment = Payments.FirstOrDefault(p => p.Id == CurrentPayment.Id);
                    if (existingPayment != null)
                    {
                        existingPayment.Amount = CurrentPayment.Amount;
                        existingPayment.Type = CurrentPayment.Type;
                        existingPayment.DueDate = CurrentPayment.DueDate;
                        existingPayment.Status = CurrentPayment.Status;
                        existingPayment.Description = CurrentPayment.Description;
                    }
                }
                else
                {
                    // Yeni ekleme
                    string newId = await _firebaseService.AddPaymentAsync(CurrentPayment);
                    CurrentPayment.Id = newId;

                    Payments.Insert(0, new Payment
                    {
                        Id = CurrentPayment.Id,
                        FlatId = CurrentPayment.FlatId,
                        TenantId = CurrentPayment.TenantId,
                        ApartmentId = CurrentPayment.ApartmentId,
                        SiteId = CurrentPayment.SiteId,
                        Amount = CurrentPayment.Amount,
                        Type = CurrentPayment.Type,
                        DueDate = CurrentPayment.DueDate,
                        Status = CurrentPayment.Status,
                        Description = CurrentPayment.Description,
                        CreatedDate = CurrentPayment.CreatedDate,
                        CreatedBy = CurrentPayment.CreatedBy
                    });

                    SuccessMessage = "Ödeme başarıyla eklendi";
                }

                CancelEdit();
            }
            catch (Exception ex)
            {
                ErrorMessage = $"Ödeme kaydedilirken hata oluştu: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task MarkAsPaidAsync()
        {
            try
            {
                if (SelectedPayment == null) return;

                IsLoading = true;
                ClearMessages();

                var currentUser = _authService.CurrentUser;
                await _firebaseService.MarkPaymentAsPaidAsync(
                    SelectedPayment.Id,
                    PaymentMethods.Cash, // Default method, can be made configurable
                    $"RCP-{DateTime.Now:yyyyMMddHHmmss}",
                    currentUser?.Id ?? "system"
                );

                SelectedPayment.Status = PaymentStatus.Paid;
                SelectedPayment.PaidDate = DateTime.Now;
                SelectedPayment.PaymentMethod = PaymentMethods.Cash;

                SuccessMessage = "Ödeme başarıyla işaretlendi";
                await LoadPaymentsAsync(); // Refresh the list
            }
            catch (Exception ex)
            {
                ErrorMessage = $"Ödeme işaretlenirken hata oluştu: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task SendReminderAsync()
        {
            try
            {
                if (SelectedPayment == null) return;

                IsLoading = true;
                ClearMessages();

                // Get tenant info
                var tenant = await _firebaseService.GetTenantAsync(SelectedPayment.TenantId);
                if (tenant == null)
                {
                    ErrorMessage = "Kiracı bilgisi bulunamadı";
                    return;
                }

                // Get flat info
                var flat = await _firebaseService.GetFlatAsync(SelectedPayment.FlatId);
                if (flat == null)
                {
                    ErrorMessage = "Daire bilgisi bulunamadı";
                    return;
                }

                // Send reminder based on payment status
                if (SelectedPayment.IsOverdue)
                {
                    await _notificationService.SendOverduePaymentNotificationsAsync(SelectedPayment.SiteId);
                }
                else
                {
                    await _notificationService.SendPaymentRemindersAsync(SelectedPayment.SiteId, 0);
                }

                SuccessMessage = "Hatırlatma gönderildi";
            }
            catch (Exception ex)
            {
                ErrorMessage = $"Hatırlatma gönderilirken hata oluştu: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task GenerateMonthlyPaymentsAsync()
        {
            try
            {
                IsLoading = true;
                ClearMessages();

                var flats = await _firebaseService.GetFlatsBySiteAsync(SelectedSiteId);
                var occupiedFlats = flats.Where(f => f.IsOccupied).ToList();

                int generatedCount = 0;
                var nextMonth = DateTime.Now.AddMonths(1);
                var dueDate = new DateTime(nextMonth.Year, nextMonth.Month, 5); // 5th of next month

                foreach (var flat in occupiedFlats)
                {
                    // Check if payments already exist for next month
                    var existingPayments = await _firebaseService.GetPaymentsByFlatAsync(flat.Id);
                    var hasRentPayment = existingPayments.Any(p =>
                        p.Type == PaymentTypes.Rent &&
                        p.DueDate.Year == dueDate.Year &&
                        p.DueDate.Month == dueDate.Month);
                    var hasDuesPayment = existingPayments.Any(p =>
                        p.Type == PaymentTypes.Dues &&
                        p.DueDate.Year == dueDate.Year &&
                        p.DueDate.Month == dueDate.Month);

                    var tenant = await _firebaseService.GetTenantByFlatAsync(flat.Id);
                    var currentUser = _authService.CurrentUser;

                    // Generate rent payment if not exists
                    if (!hasRentPayment && flat.RentAmount > 0)
                    {
                        var rentPayment = new Payment
                        {
                            FlatId = flat.Id,
                            TenantId = tenant?.Id ?? "",
                            ApartmentId = flat.ApartmentId,
                            SiteId = flat.SiteId,
                            Amount = flat.RentAmount,
                            Type = PaymentTypes.Rent,
                            DueDate = dueDate,
                            Status = PaymentStatus.Due,
                            Description = $"{dueDate:MMMM yyyy} Kira Ödemesi",
                            CreatedBy = currentUser?.Id ?? "system"
                        };

                        await _firebaseService.AddPaymentAsync(rentPayment);
                        generatedCount++;
                    }

                    // Generate dues payment if not exists
                    if (!hasDuesPayment && flat.Dues > 0)
                    {
                        var duesPayment = new Payment
                        {
                            FlatId = flat.Id,
                            TenantId = tenant?.Id ?? "",
                            ApartmentId = flat.ApartmentId,
                            SiteId = flat.SiteId,
                            Amount = flat.Dues,
                            Type = PaymentTypes.Dues,
                            DueDate = dueDate,
                            Status = PaymentStatus.Due,
                            Description = $"{dueDate:MMMM yyyy} Aidat Ödemesi",
                            CreatedBy = currentUser?.Id ?? "system"
                        };

                        await _firebaseService.AddPaymentAsync(duesPayment);
                        generatedCount++;
                    }
                }

                SuccessMessage = $"{generatedCount} ödeme kaydı oluşturuldu";
                await LoadPaymentsAsync();
            }
            catch (Exception ex)
            {
                ErrorMessage = $"Aylık ödemeler oluşturulurken hata oluştu: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task DeletePaymentAsync()
        {
            try
            {
                if (SelectedPayment == null) return;

                IsLoading = true;
                ClearMessages();

                // Note: In a real system, you might want to soft delete or archive payments
                // For now, we'll remove from the list
                Payments.Remove(SelectedPayment);
                SelectedPayment = null;
                CancelEdit();

                SuccessMessage = "Ödeme başarıyla silindi";
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                ErrorMessage = $"Ödeme silinirken hata oluştu: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        private void EditPayment()
        {
            if (SelectedPayment != null)
            {
                IsEditing = true;
            }
        }

        private void NewPayment()
        {
            CurrentPayment = new Payment
            {
                SiteId = SelectedSiteId,
                DueDate = DateTime.Now.AddDays(30),
                Status = PaymentStatus.Due
            };
            IsEditing = false;
            SelectedPayment = null;
            ClearMessages();
        }

        private void CancelEdit()
        {
            CurrentPayment = new Payment
            {
                SiteId = SelectedSiteId,
                DueDate = DateTime.Now.AddDays(30),
                Status = PaymentStatus.Due
            };
            IsEditing = false;
            SelectedPayment = null;
            ClearMessages();
        }

        private bool CanSavePayment()
        {
            return CurrentPayment.Amount > 0 &&
                   !string.IsNullOrWhiteSpace(CurrentPayment.Type) &&
                   !string.IsNullOrWhiteSpace(CurrentPayment.FlatId) &&
                   !string.IsNullOrWhiteSpace(CurrentPayment.SiteId) &&
                   !IsLoading;
        }

        private bool CanDeletePayment()
        {
            var currentUser = _authService.CurrentUser;
            return SelectedPayment != null &&
                   !IsLoading &&
                   (currentUser?.Role == UserRoles.Admin || currentUser?.Role == UserRoles.Manager);
        }

        private bool CanMarkAsPaid()
        {
            return SelectedPayment != null &&
                   SelectedPayment.Status != PaymentStatus.Paid &&
                   !IsLoading;
        }

        private void ClearMessages()
        {
            ErrorMessage = string.Empty;
            SuccessMessage = string.Empty;
        }
    }
}