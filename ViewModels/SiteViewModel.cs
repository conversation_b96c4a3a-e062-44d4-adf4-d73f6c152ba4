using ApartmanYonetimSistemi.Models;
using ApartmanYonetimSistemi.Helpers;
using ApartmanYonetimSistemi.Services;
using System.Collections.ObjectModel;
using System.ComponentModel.DataAnnotations;

namespace ApartmanYonetimSistemi.ViewModels
{
    public class SiteViewModel : BaseViewModel
    {
        private ObservableCollection<Site> _sites = new();
        private Site? _selectedSite;
        private Site _currentSite = new();
        private bool _isLoading = false;
        private bool _isEditing = false;
        private string _errorMessage = string.Empty;
        private string _successMessage = string.Empty;
        private readonly FirebaseService _firebaseService;
        private readonly AuthService _authService;

        public ObservableCollection<Site> Sites
        {
            get => _sites;
            set => SetProperty(ref _sites, value);
        }

        public Site? SelectedSite
        {
            get => _selectedSite;
            set
            {
                if (SetProperty(ref _selectedSite, value) && value != null)
                {
                    CurrentSite = value;
                    IsEditing = false;
                }
            }
        }

        public Site CurrentSite
        {
            get => _currentSite;
            set => SetProperty(ref _currentSite, value);
        }

        public bool IsLoading
        {
            get => _isLoading;
            set => SetProperty(ref _isLoading, value);
        }

        public bool IsEditing
        {
            get => _isEditing;
            set => SetProperty(ref _isEditing, value);
        }

        public string ErrorMessage
        {
            get => _errorMessage;
            set => SetProperty(ref _errorMessage, value);
        }

        public string SuccessMessage
        {
            get => _successMessage;
            set => SetProperty(ref _successMessage, value);
        }

        // Commands
        public RelayCommand LoadSitesCommand { get; }
        public RelayCommand NewSiteCommand { get; }
        public RelayCommand EditCommand { get; }
        public RelayCommand SaveSiteCommand { get; }
        public RelayCommand DeleteSiteCommand { get; }
        public RelayCommand CancelEditCommand { get; }

        public SiteViewModel()
        {
            _firebaseService = new FirebaseService();
            _authService = new AuthService();

            LoadSitesCommand = new RelayCommand(async _ => await LoadSitesAsync());
            NewSiteCommand = new RelayCommand(p => ExecuteNewSite());
            EditCommand = new RelayCommand(p => ExecuteEdit(), p => SelectedSite != null && !IsEditing);
            SaveSiteCommand = new RelayCommand(async _ => await ExecuteSaveSiteAsync(), _ => CanSaveSite());
            DeleteSiteCommand = new RelayCommand(async _ => await ExecuteDeleteSiteAsync(), _ => SelectedSite != null);
            CancelEditCommand = new RelayCommand(p => ExecuteCancelEdit());

            PropertyChanged += (s, e) =>
            {
                if (e.PropertyName == nameof(SelectedSite) || e.PropertyName == nameof(IsEditing))
                {
                    EditCommand.RaiseCanExecuteChanged();
                    DeleteSiteCommand.RaiseCanExecuteChanged();
                }
            };
        }

        public async Task InitializeAsync()
        {
            await LoadSitesAsync();
        }

        private async Task LoadSitesAsync()
        {
            try
            {
                IsLoading = true;
                Sites.Clear();
                ClearMessages();

                var currentUser = _authService.CurrentUser;
                if (currentUser == null) return;

                List<Site> sites;
                if (currentUser.Role == UserRoles.Admin)
                {
                    sites = await _firebaseService.GetAllSitesAsync();
                }
                else
                {
                    sites = await _firebaseService.GetSitesByOwnerAsync(currentUser.Id);
                }

                foreach (var site in sites)
                {
                    Sites.Add(site);
                }
            }
            catch (Exception ex)
            {
                ErrorMessage = $"Siteler yüklenirken hata oluştu: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        private void ExecuteNewSite()
        {
            CurrentSite = new Site { IsActive = true };
            IsEditing = true;
        }

        private void ExecuteEdit()
        {
            CurrentSite = SelectedSite!.Clone();
            IsEditing = true;
        }

        private async Task ExecuteSaveSiteAsync()
        {
            try
            {
                IsLoading = true;
                ClearMessages();

                var validationResults = new List<ValidationResult>();
                var validationContext = new ValidationContext(CurrentSite);
                if (!Validator.TryValidateObject(CurrentSite, validationContext, validationResults, true))
                {
                    ErrorMessage = string.Join("\n", validationResults.Select(r => r.ErrorMessage));
                    return;
                }

                var currentUser = _authService.CurrentUser;
                if (currentUser == null)
                {
                    ErrorMessage = "Kullanıcı oturumu bulunamadı";
                    IsLoading = false;
                    return;
                }

                bool isNew = string.IsNullOrEmpty(CurrentSite.Id);

                if (!isNew)
                {
                    await _firebaseService.UpdateSiteAsync(CurrentSite);
                    SuccessMessage = "Site başarıyla güncellendi";

                    var index = Sites.ToList().FindIndex(s => s.Id == CurrentSite.Id);
                    if (index != -1)
                    {
                        Sites[index] = CurrentSite.Clone();
                    }
                }
                else
                {
                    CurrentSite.OwnerId = currentUser.Id;
                    string newId = await _firebaseService.AddSiteAsync(CurrentSite);
                    CurrentSite.Id = newId;
                    Sites.Add(CurrentSite.Clone());
                    SuccessMessage = "Site başarıyla eklendi";
                }

                CurrentSite = new Site();
                SelectedSite = null;
                IsEditing = false;
            }
            catch (Exception ex)
            {
                ErrorMessage = $"Site kaydedilirken hata oluştu: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task ExecuteDeleteSiteAsync()
        {
            try
            {
                if (SelectedSite == null) return;

                IsLoading = true;
                ClearMessages();

                await _firebaseService.DeleteSiteAsync(SelectedSite.Id);
                Sites.Remove(SelectedSite);
                
                CurrentSite = new Site();
                SelectedSite = null;
                IsEditing = false;

                SuccessMessage = "Site başarıyla silindi";
            }
            catch (Exception ex)
            {
                ErrorMessage = $"Site silinirken hata oluştu: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        private void ExecuteCancelEdit()
        {
            IsEditing = false;
            if (SelectedSite != null)
            {
                CurrentSite = SelectedSite;
            }
            else
            {
                CurrentSite = new Site();
            }
            ClearMessages();
        }

        private bool CanSaveSite()
        {
            return IsEditing && !string.IsNullOrWhiteSpace(CurrentSite.SiteName) && !string.IsNullOrWhiteSpace(CurrentSite.Address);
        }

        private void ClearMessages()
        {
            ErrorMessage = string.Empty;
            SuccessMessage = string.Empty;
        }
    }
}