using ApartmanYonetimSistemi.Helpers;
using ApartmanYonetimSistemi.Services;
using ApartmanYonetimSistemi.Models;
using System.Windows;

namespace ApartmanYonetimSistemi.ViewModels
{
    public class LoginViewModel : BaseViewModel
    {
        private string _email = string.Empty;
        private string _password = string.Empty;
        private string _name = string.Empty;
        private string _company = string.Empty;
        private string _errorMessage = string.Empty;
        private string _successMessage = string.Empty;
        private bool _isLoading = false;
        private bool _isLoginMode = true;
        private readonly AuthService _authService;

        public string Email
        {
            get => _email;
            set => SetProperty(ref _email, value);
        }

        public string Password
        {
            get => _password;
            set => SetProperty(ref _password, value);
        }

        public string Name
        {
            get => _name;
            set => SetProperty(ref _name, value);
        }

        public string Company
        {
            get => _company;
            set => SetProperty(ref _company, value);
        }

        public string ErrorMessage
        {
            get => _errorMessage;
            set => SetProperty(ref _errorMessage, value);
        }

        public string SuccessMessage
        {
            get => _successMessage;
            set => SetProperty(ref _successMessage, value);
        }

        public bool IsLoading
        {
            get => _isLoading;
            set => SetProperty(ref _isLoading, value);
        }

        public bool IsLoginMode
        {
            get => _isLoginMode;
            set => SetProperty(ref _isLoginMode, value);
        }

        public RelayCommand LoginCommand { get; }
        public RelayCommand RegisterCommand { get; }
        public RelayCommand SwitchModeCommand { get; }
        public RelayCommand ForgotPasswordCommand { get; }

        public event EventHandler<User>? LoginSuccessful;

        public LoginViewModel()
        {
            _authService = new AuthService();
            LoginCommand = new RelayCommand(async _ => await LoginAsync(), _ => !IsLoading && CanLogin());
            RegisterCommand = new RelayCommand(async _ => await RegisterAsync(), _ => !IsLoading && CanRegister());
            SwitchModeCommand = new RelayCommand(_ => SwitchMode());
            ForgotPasswordCommand = new RelayCommand(async _ => await ForgotPasswordAsync(), _ => !IsLoading && !string.IsNullOrEmpty(Email));
        }

        private bool CanLogin()
        {
            return !string.IsNullOrEmpty(Email) && !string.IsNullOrEmpty(Password);
        }

        private bool CanRegister()
        {
            return !string.IsNullOrEmpty(Email) && !string.IsNullOrEmpty(Password) && !string.IsNullOrEmpty(Name);
        }

        private async Task LoginAsync()
        {
            try
            {
                IsLoading = true;
                ErrorMessage = string.Empty;
                SuccessMessage = string.Empty;

                var result = await _authService.LoginAsync(Email, Password);

                if (result.Success && result.User != null)
                {
                    SuccessMessage = "Giriş başarılı!";
                    LoginSuccessful?.Invoke(this, result.User);
                }
                else
                {
                    ErrorMessage = result.Message;
                }
            }
            catch (Exception ex)
            {
                ErrorMessage = $"Giriş sırasında hata oluştu: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task RegisterAsync()
        {
            try
            {
                IsLoading = true;
                ErrorMessage = string.Empty;
                SuccessMessage = string.Empty;

                var result = await _authService.RegisterAsync(Email, Password, Name, Company);

                if (result.Success && result.User != null)
                {
                    SuccessMessage = "Kayıt başarılı! Giriş yapabilirsiniz.";
                    IsLoginMode = true;
                    ClearForm();
                }
                else
                {
                    ErrorMessage = result.Message;
                }
            }
            catch (Exception ex)
            {
                ErrorMessage = $"Kayıt sırasında hata oluştu: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task ForgotPasswordAsync()
        {
            try
            {
                IsLoading = true;
                ErrorMessage = string.Empty;
                SuccessMessage = string.Empty;

                var result = await _authService.ResetPasswordAsync(Email);

                if (result.Success)
                {
                    SuccessMessage = result.Message;
                }
                else
                {
                    ErrorMessage = result.Message;
                }
            }
            catch (Exception ex)
            {
                ErrorMessage = $"Şifre sıfırlama sırasında hata oluştu: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        private void SwitchMode()
        {
            IsLoginMode = !IsLoginMode;
            ClearMessages();
        }

        private void ClearForm()
        {
            Email = string.Empty;
            Password = string.Empty;
            Name = string.Empty;
            Company = string.Empty;
            ClearMessages();
        }

        private void ClearMessages()
        {
            ErrorMessage = string.Empty;
            SuccessMessage = string.Empty;
        }
    }
}