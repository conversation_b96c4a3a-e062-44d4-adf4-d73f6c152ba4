using ApartmanYonetimSistemi.Models;
using ApartmanYonetimSistemi.Helpers;
using ApartmanYonetimSistemi.Services;
using System.Collections.ObjectModel;
using System.ComponentModel.DataAnnotations;

namespace ApartmanYonetimSistemi.ViewModels
{
    public class FlatViewModel : BaseViewModel
    {
        private ObservableCollection<Flat> _flats = new();
        private Flat? _selectedFlat;
        private Flat _currentFlat = new();
        private bool _isLoading = false;
        private bool _isEditing = false;
        private string _errorMessage = string.Empty;
        private string _successMessage = string.Empty;
        private string _selectedSiteId = string.Empty;
        private string _selectedApartmentId = string.Empty;
        private readonly FirebaseService _firebaseService;
        private readonly AuthService _authService;

        public ObservableCollection<Flat> Flats
        {
            get => _flats;
            set => SetProperty(ref _flats, value);
        }

        public Flat? SelectedFlat
        {
            get => _selectedFlat;
            set
            {
                if (SetProperty(ref _selectedFlat, value) && value != null)
                {
                    CurrentFlat = value;
                    IsEditing = false;
                }
            }
        }

        public Flat CurrentFlat
        {
            get => _currentFlat;
            set => SetProperty(ref _currentFlat, value);
        }

        public string SelectedSiteId
        {
            get => _selectedSiteId;
            set
            {
                if (SetProperty(ref _selectedSiteId, value))
                {
                    CurrentFlat.SiteId = value;
                    _ = LoadFlatsAsync();
                }
            }
        }

        public string SelectedApartmentId
        {
            get => _selectedApartmentId;
            set
            {
                if (SetProperty(ref _selectedApartmentId, value))
                {
                    CurrentFlat.ApartmentId = value;
                    _ = LoadFlatsAsync();
                }
            }
        }

        public bool IsLoading
        {
            get => _isLoading;
            set => SetProperty(ref _isLoading, value);
        }

        public bool IsEditing
        {
            get => _isEditing;
            set => SetProperty(ref _isEditing, value);
        }

        public string ErrorMessage
        {
            get => _errorMessage;
            set => SetProperty(ref _errorMessage, value);
        }

        public string SuccessMessage
        {
            get => _successMessage;
            set => SetProperty(ref _successMessage, value);
        }

        // Commands
        public RelayCommand LoadFlatsCommand { get; }
        public RelayCommand NewFlatCommand { get; }
        public RelayCommand EditCommand { get; }
        public RelayCommand SaveFlatCommand { get; }
        public RelayCommand DeleteFlatCommand { get; }
        public RelayCommand CancelEditCommand { get; }

        public FlatViewModel()
        {
            _firebaseService = new FirebaseService();
            _authService = new AuthService();

            LoadFlatsCommand = new RelayCommand(async _ => await LoadFlatsAsync());
            NewFlatCommand = new RelayCommand(p => ExecuteNewFlat(), p => !string.IsNullOrEmpty(SelectedApartmentId));
            EditCommand = new RelayCommand(p => ExecuteEdit(), p => SelectedFlat != null && !IsEditing);
            SaveFlatCommand = new RelayCommand(async _ => await ExecuteSaveFlatAsync(), _ => CanSaveFlat());
            DeleteFlatCommand = new RelayCommand(async _ => await ExecuteDeleteFlatAsync(), _ => SelectedFlat != null);
            CancelEditCommand = new RelayCommand(p => ExecuteCancelEdit());
            
            PropertyChanged += (s, e) =>
            {
                if (e.PropertyName == nameof(SelectedFlat) || e.PropertyName == nameof(IsEditing))
                {
                    EditCommand.RaiseCanExecuteChanged();
                    DeleteFlatCommand.RaiseCanExecuteChanged();
                }
                if (e.PropertyName == nameof(SelectedApartmentId))
                {
                    NewFlatCommand.RaiseCanExecuteChanged();
                    _ = LoadFlatsAsync(); // Apartman seçimi değiştiğinde daireleri yükle
                }
            };
        }

        private async Task LoadFlatsAsync()
        {
            if (string.IsNullOrEmpty(SelectedSiteId)) return;

            try
            {
                IsLoading = true;
                Flats.Clear();
                ClearMessages();

                List<Flat> flats;
                if (!string.IsNullOrEmpty(SelectedApartmentId))
                {
                    flats = await _firebaseService.GetFlatsByApartmentAsync(SelectedApartmentId);
                }
                else
                {
                    flats = await _firebaseService.GetFlatsBySiteAsync(SelectedSiteId);
                }

                foreach (var flat in flats)
                {
                    Flats.Add(flat);
                }
            }
            catch (Exception ex)
            {
                ErrorMessage = $"Daireler yüklenirken hata oluştu: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        private void ExecuteNewFlat()
        {
            CurrentFlat = new Flat
            {
                SiteId = SelectedSiteId,
                ApartmentId = SelectedApartmentId,
                IsActive = true,
                RentStatus = "Due",
                DuesStatus = "Due"
            };
            IsEditing = true;
        }

        private void ExecuteEdit()
        {
            CurrentFlat = SelectedFlat!.Clone();
            IsEditing = true;
        }

        private async Task ExecuteSaveFlatAsync()
        {
            try
            {
                if (string.IsNullOrEmpty(CurrentFlat.ApartmentId) || string.IsNullOrEmpty(CurrentFlat.FlatNo))
                {
                    ErrorMessage = "Lütfen tüm alanları doldurun";
                    return;
                }

                IsLoading = true;
                var currentUser = _authService.CurrentUser;
                if (currentUser == null)
                {
                    ErrorMessage = "Kullanıcı oturumu bulunamadı";
                    IsLoading = false;
                    return;
                }

                bool isNew = string.IsNullOrEmpty(CurrentFlat.Id);

                if (!isNew)
                {
                    await _firebaseService.UpdateFlatAsync(CurrentFlat);
                    SuccessMessage = "Daire başarıyla güncellendi";

                    var index = Flats.ToList().FindIndex(f => f.Id == CurrentFlat.Id);
                    if (index != -1)
                    {
                        Flats[index] = CurrentFlat.Clone();
                    }
                }
                else
                {
                    CurrentFlat.CreatedById = currentUser.Id;
                    string newId = await _firebaseService.AddFlatAsync(CurrentFlat);
                    CurrentFlat.Id = newId;
                    Flats.Add(CurrentFlat.Clone());
                    SuccessMessage = "Daire başarıyla eklendi";
                }

                CurrentFlat = new Flat { SiteId = _selectedSiteId, ApartmentId = _selectedApartmentId };
                SelectedFlat = null;
                IsEditing = false;
            }
            catch (Exception ex)
            {
                ErrorMessage = $"Daire kaydedilirken hata oluştu: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task ExecuteDeleteFlatAsync()
        {
            try
            {
                if (SelectedFlat == null) return;

                IsLoading = true;
                ClearMessages();

                await _firebaseService.DeleteFlatAsync(SelectedFlat.Id);
                Flats.Remove(SelectedFlat);
                
                CurrentFlat = new Flat();
                SelectedFlat = null;
                IsEditing = false;
                
                SuccessMessage = "Daire başarıyla silindi";
            }
            catch (Exception ex)
            {
                ErrorMessage = $"Daire silinirken hata oluştu: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        private void ExecuteCancelEdit()
        {
            IsEditing = false;
            if (SelectedFlat != null)
            {
                CurrentFlat = SelectedFlat;
            }
            else
            {
                CurrentFlat = new Flat();
            }
            ClearMessages();
        }

        private bool CanSaveFlat()
        {
            return IsEditing &&
                   !string.IsNullOrWhiteSpace(CurrentFlat.FlatNo) &&
                   CurrentFlat.RentAmount >= 0 &&
                   CurrentFlat.Dues >= 0;
        }

        private void ClearMessages()
        {
            ErrorMessage = string.Empty;
            SuccessMessage = string.Empty;
        }
    }
}