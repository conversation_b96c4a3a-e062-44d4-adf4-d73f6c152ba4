using ApartmanYonetimSistemi.Models;
using ApartmanYonetimSistemi.Helpers;
using ApartmanYonetimSistemi.Services;
using System.Collections.ObjectModel;
using System.ComponentModel.DataAnnotations;

namespace ApartmanYonetimSistemi.ViewModels
{
    public class TenantViewModel : BaseViewModel
    {
        private ObservableCollection<Tenant> _tenants = new();
        private ObservableCollection<Flat> _availableFlats = new();
        private Tenant? _selectedTenant;
        private Tenant _currentTenant = new();
        private bool _isLoading = false;
        private bool _isEditing = false;
        private string _errorMessage = string.Empty;
        private string _successMessage = string.Empty;
        private string _selectedSiteId = string.Empty;
        private string _selectedApartmentId = string.Empty;
        private string _selectedFlatId = string.Empty;
        private readonly FirebaseService _firebaseService;
        private readonly AuthService _authService;
        private readonly NotificationService _notificationService;

        public ObservableCollection<Tenant> Tenants
        {
            get => _tenants;
            set => SetProperty(ref _tenants, value);
        }

        public ObservableCollection<Flat> AvailableFlats
        {
            get => _availableFlats;
            set => SetProperty(ref _availableFlats, value);
        }

        public Tenant? SelectedTenant
        {
            get => _selectedTenant;
            set
            {
                if (SetProperty(ref _selectedTenant, value) && value != null)
                {
                    CurrentTenant = value;
                    IsEditing = false;
                }
            }
        }

        public Tenant CurrentTenant
        {
            get => _currentTenant;
            set => SetProperty(ref _currentTenant, value);
        }

        public string SelectedSiteId
        {
            get => _selectedSiteId;
            set
            {
                if (SetProperty(ref _selectedSiteId, value))
                {
                    // Site değiştiğinde diğer seçimleri temizle ve yeniden yükle
                    SelectedApartmentId = string.Empty;
                    SelectedFlatId = string.Empty;
                    Tenants.Clear();
                    AvailableFlats.Clear();
                }
            }
        }

        public string SelectedApartmentId
        {
            get => _selectedApartmentId;
            set
            {
                if (SetProperty(ref _selectedApartmentId, value))
                {
                    // Apartman değiştiğinde daire seçimini temizle ve yeniden yükle
                    SelectedFlatId = string.Empty;
                    Tenants.Clear();
                    AvailableFlats.Clear();
                }
            }
        }

        public string SelectedFlatId
        {
            get => _selectedFlatId;
            set
            {
                if (SetProperty(ref _selectedFlatId, value))
                {
                    // Daire seçimi değiştiğinde kiracıları yükle
                    _ = LoadTenantsAsync();
                }
            }
        }

        public bool IsLoading
        {
            get => _isLoading;
            set => SetProperty(ref _isLoading, value);
        }

        public bool IsEditing
        {
            get => _isEditing;
            set => SetProperty(ref _isEditing, value);
        }

        public string ErrorMessage
        {
            get => _errorMessage;
            set => SetProperty(ref _errorMessage, value);
        }

        public string SuccessMessage
        {
            get => _successMessage;
            set => SetProperty(ref _successMessage, value);
        }

        // Commands
        public RelayCommand LoadTenantsCommand { get; }
        public RelayCommand NewTenantCommand { get; }
        public RelayCommand EditCommand { get; }
        public RelayCommand SaveTenantCommand { get; }
        public RelayCommand DeleteTenantCommand { get; }
        public RelayCommand CancelEditCommand { get; }
        public RelayCommand SendWelcomeEmailCommand { get; }

        public TenantViewModel()
        {
            _firebaseService = new FirebaseService();
            _authService = new AuthService();
            _notificationService = new NotificationService();

            LoadTenantsCommand = new RelayCommand(async _ => await LoadTenantsAsync());
            NewTenantCommand = new RelayCommand(p => ExecuteNewTenant(), p => !string.IsNullOrEmpty(SelectedFlatId));
            EditCommand = new RelayCommand(p => ExecuteEdit(), p => SelectedTenant != null && !IsEditing);
            SaveTenantCommand = new RelayCommand(async _ => await ExecuteSaveTenantAsync(), _ => CanSaveTenant());
            DeleteTenantCommand = new RelayCommand(async _ => await ExecuteDeleteTenantAsync(), _ => SelectedTenant != null);
            CancelEditCommand = new RelayCommand(p => ExecuteCancelEdit());
            SendWelcomeEmailCommand = new RelayCommand(async _ => await ExecuteSendWelcomeEmailAsync(), _ => SelectedTenant != null);

            PropertyChanged += (s, e) =>
            {
                if (e.PropertyName == nameof(SelectedTenant) || e.PropertyName == nameof(IsEditing))
                {
                    EditCommand.RaiseCanExecuteChanged();
                    DeleteTenantCommand.RaiseCanExecuteChanged();
                    SendWelcomeEmailCommand.RaiseCanExecuteChanged();
                }
                if (e.PropertyName == nameof(SelectedSiteId) || e.PropertyName == nameof(SelectedApartmentId) || e.PropertyName == nameof(SelectedFlatId))
                {
                    NewTenantCommand.RaiseCanExecuteChanged();
                    _ = LoadTenantsAsync();
                    _ = LoadAvailableFlatsAsync();
                }
            };
        }

        private async Task LoadTenantsAsync()
        {
            if (string.IsNullOrEmpty(SelectedFlatId)) return;

            try
            {
                IsLoading = true;
                Tenants.Clear();
                ClearMessages();

                var tenants = await _firebaseService.GetTenantsByFlatAsync(SelectedFlatId);
                foreach (var tenant in tenants)
                {
                    Tenants.Add(tenant);
                }
            }
            catch (Exception ex)
            {
                ErrorMessage = $"Kiracılar yüklenirken hata oluştu: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        private void ExecuteNewTenant()
        {
            CurrentTenant = new Tenant
            {
                SiteId = SelectedSiteId,
                ApartmentId = SelectedApartmentId,
                FlatId = SelectedFlatId,
                EntryDate = DateTime.Now,
                IsActive = true
            };
            IsEditing = true;
        }

        private void ExecuteEdit()
        {
            CurrentTenant = SelectedTenant!.Clone();
            IsEditing = true;
        }

        private async Task LoadAvailableFlatsAsync()
        {
            if (string.IsNullOrEmpty(SelectedApartmentId)) return;

            try
            {
                AvailableFlats.Clear();
                var flats = await _firebaseService.GetFlatsByApartmentAsync(SelectedApartmentId);
                
                // Sadece boş olan veya şu an düzenlediğimiz/seçtiğimiz kiracıya ait olan daireleri listele.
                var flatIdToInclude = IsEditing ? CurrentTenant?.FlatId : null;
                var availableFlats = flats.Where(f => !f.IsOccupied || f.Id == flatIdToInclude).ToList();

                foreach (var flat in availableFlats)
                {
                    AvailableFlats.Add(flat);
                }
            }
            catch (Exception ex)
            {
                ErrorMessage = $"Daireler yüklenirken hata oluştu: {ex.Message}";
            }
        }

        private async Task ExecuteSaveTenantAsync()
        {
            try
            {
                IsLoading = true;
                var currentUser = _authService.CurrentUser;
                if (currentUser == null)
                {
                    ErrorMessage = "Kullanıcı oturumu bulunamadı";
                    IsLoading = false;
                    return;
                }

                bool isNew = string.IsNullOrEmpty(CurrentTenant.Id);

                if (!isNew) // Güncelleme
                {
                    CurrentTenant.LastUpdatedById = currentUser.Id;
                    await _firebaseService.UpdateTenantAsync(CurrentTenant);
                    SuccessMessage = "Kiracı başarıyla güncellendi";

                    // Koleksiyonu güncelle
                    var index = Tenants.ToList().FindIndex(t => t.Id == CurrentTenant.Id);
                    if (index != -1)
                    {
                        await UpdateFlatOccupancyAsync(Tenants[index].FlatId, false); // Eski daireyi boşalt
                        Tenants[index] = CurrentTenant.Clone();
                    }
                }
                else // Yeni Ekleme
                {
                    CurrentTenant.CreatedById = currentUser.Id;
                    string newId = await _firebaseService.AddTenantAsync(CurrentTenant);
                    CurrentTenant.Id = newId;
                    Tenants.Add(CurrentTenant.Clone());
                    SuccessMessage = "Kiracı başarıyla eklendi";
                }

                // Dairenin IsOccupied durumunu güncelle
                await UpdateFlatOccupancyAsync(CurrentTenant.FlatId, true);

                CurrentTenant = new Tenant { SiteId = SelectedSiteId, ApartmentId = SelectedApartmentId, FlatId = SelectedFlatId }; // Formu temizle
                SelectedTenant = null;
                IsEditing = false;
            }
            catch (Exception ex)
            {
                ErrorMessage = $"Kiracı kaydedilirken hata oluştu: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task UpdateFlatOccupancyAsync(string? flatId, bool isOccupied)
        {
            if (string.IsNullOrEmpty(flatId)) return;

            try
            {
                var flat = await _firebaseService.GetFlatAsync(flatId);
                if (flat != null)
                {
                    flat.IsOccupied = isOccupied;
                    await _firebaseService.UpdateFlatAsync(flat);
                }
            }
            catch (Exception ex)
            {
                // Log error but don't show to user
                System.Diagnostics.Debug.WriteLine($"Error updating flat occupancy: {ex.Message}");
            }
        }

        private async Task ExecuteDeleteTenantAsync()
        {
            try
            {
                if (SelectedTenant == null) return;

                IsLoading = true;
                ClearMessages();

                string? flatToVacate = SelectedTenant.FlatId;

                await _firebaseService.DeleteTenantAsync(SelectedTenant.Id);
                Tenants.Remove(SelectedTenant);

                // Daireyi boşalt
                await UpdateFlatOccupancyAsync(flatToVacate, false);

                CurrentTenant = new Tenant();
                SelectedTenant = null;
                IsEditing = false;
                await LoadAvailableFlatsAsync();

                SuccessMessage = "Kiracı başarıyla silindi";
            }
            catch (Exception ex)
            {
                ErrorMessage = $"Kiracı silinirken hata oluştu: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task ExecuteSendWelcomeEmailAsync()
        {
            try
            {
                if (SelectedTenant == null) return;

                IsLoading = true;
                ClearMessages();
                await _notificationService.SendWelcomeNotificationAsync(SelectedTenant.Id);
                SuccessMessage = "Hoş geldin e-postası başarıyla gönderildi.";
            }
            catch (Exception ex)
            {
                ErrorMessage = $"E-posta gönderilirken hata oluştu: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        private void ExecuteCancelEdit()
        {
            IsEditing = false;
            if (SelectedTenant != null)
            {
                CurrentTenant = SelectedTenant;
            }
            else
            {
                CurrentTenant = new Tenant();
            }
            ClearMessages();
            _ = LoadAvailableFlatsAsync(); // Listeyi eski haline getir.
        }

        private bool CanSaveTenant()
        {
            return IsEditing &&
                   !string.IsNullOrWhiteSpace(CurrentTenant.Name) &&
                   !string.IsNullOrWhiteSpace(CurrentTenant.Surname) &&
                   !string.IsNullOrWhiteSpace(CurrentTenant.FlatId);
        }

        private void ClearMessages()
        {
            ErrorMessage = string.Empty;
            SuccessMessage = string.Empty;
        }
    }
}