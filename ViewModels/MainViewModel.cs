using ApartmanYonetimSistemi.Models;
using ApartmanYonetimSistemi.Helpers;
using ApartmanYonetimSistemi.Services;
using System.Collections.ObjectModel;

namespace ApartmanYonetimSistemi.ViewModels
{
    public class MainViewModel : BaseViewModel
    {
        private User? _currentUser;
        private Site? _selectedSite;
        private Apartment? _selectedApartment;
        private string _currentView = "Dashboard";
        private bool _isLoading = false;
        private readonly FirebaseService _firebaseService;
        private readonly AuthService _authService;
        private readonly NotificationService _notificationService;

        public ObservableCollection<Site> UserSites { get; } = new();
        public ObservableCollection<Apartment> SiteApartments { get; } = new();

        // Child ViewModels
        public SiteViewModel SiteViewModel { get; }
        public ApartmentViewModel ApartmentViewModel { get; }
        public FlatViewModel FlatViewModel { get; }
        public TenantViewModel TenantViewModel { get; }
        public PaymentViewModel PaymentViewModel { get; }
        public ReportViewModel ReportViewModel { get; }

        public User? CurrentUser
        {
            get => _currentUser;
            set => SetProperty(ref _currentUser, value);
        }

        public Site? SelectedSite
        {
            get => _selectedSite;
            set
            {
                if (SetProperty(ref _selectedSite, value))
                {
                    _ = LoadSiteApartmentsAsync();
                }
            }
        }

        public Apartment? SelectedApartment
        {
            get => _selectedApartment;
            set => SetProperty(ref _selectedApartment, value);
        }

        public string CurrentView
        {
            get => _currentView;
            set => SetProperty(ref _currentView, value);
        }

        public bool IsLoading
        {
            get => _isLoading;
            set => SetProperty(ref _isLoading, value);
        }

        // Navigation Commands
        public RelayCommand ShowDashboardCommand { get; }
        public RelayCommand ShowSitesCommand { get; }
        public RelayCommand ShowApartmentsCommand { get; }
        public RelayCommand ShowFlatsCommand { get; }
        public RelayCommand ShowTenantsCommand { get; }
        public RelayCommand ShowPaymentsCommand { get; }
        public RelayCommand ShowReportsCommand { get; }
        public RelayCommand ShowSettingsCommand { get; }
        public RelayCommand LogoutCommand { get; }

        // Action Commands
        public RelayCommand RefreshDataCommand { get; }

        public event EventHandler? LogoutRequested;

        public MainViewModel()
        {
            _firebaseService = new FirebaseService();
            _authService = new AuthService();
            _notificationService = new NotificationService();

            // Initialize child ViewModels
            SiteViewModel = new SiteViewModel();
            ApartmentViewModel = new ApartmentViewModel();
            FlatViewModel = new FlatViewModel();
            TenantViewModel = new TenantViewModel();
            PaymentViewModel = new PaymentViewModel();
            ReportViewModel = new ReportViewModel();

            // Navigation Commands - Tüm command'ları her zaman çalışabilir hale getir
            ShowDashboardCommand = new RelayCommand(_ => {
                CurrentView = "Dashboard";
                System.Diagnostics.Debug.WriteLine($"CurrentView changed to: {CurrentView}");
            });
            ShowSitesCommand = new RelayCommand(_ => {
                CurrentView = "Sites";
                System.Diagnostics.Debug.WriteLine($"CurrentView changed to: {CurrentView}");
            });
            ShowApartmentsCommand = new RelayCommand(_ => {
                CurrentView = "Apartments";
                System.Diagnostics.Debug.WriteLine($"CurrentView changed to: {CurrentView}");
            });
            ShowFlatsCommand = new RelayCommand(_ => {
                CurrentView = "Flats";
                System.Diagnostics.Debug.WriteLine($"CurrentView changed to: {CurrentView}");
            });
            ShowTenantsCommand = new RelayCommand(_ => {
                CurrentView = "Tenants";
                System.Diagnostics.Debug.WriteLine($"CurrentView changed to: {CurrentView}");
            });
            ShowPaymentsCommand = new RelayCommand(_ => {
                CurrentView = "Payments";
                System.Diagnostics.Debug.WriteLine($"CurrentView changed to: {CurrentView}");
            });
            ShowReportsCommand = new RelayCommand(_ => {
                CurrentView = "Reports";
                System.Diagnostics.Debug.WriteLine($"CurrentView changed to: {CurrentView}");
            });
            ShowSettingsCommand = new RelayCommand(_ => {
                CurrentView = "Settings";
                System.Diagnostics.Debug.WriteLine($"CurrentView changed to: {CurrentView}");
            });
            LogoutCommand = new RelayCommand(_ => Logout());

            // Action Commands
            RefreshDataCommand = new RelayCommand(async _ => await RefreshDataAsync());
        }

        public async Task InitializeAsync(User user)
        {
            CurrentUser = user;
            CurrentView = "Dashboard"; // Başlangıçta Dashboard'ı göster
            await LoadUserSitesAsync();
        }

        private async Task LoadUserSitesAsync()
        {
            try
            {
                IsLoading = true;
                UserSites.Clear();

                if (CurrentUser == null) return;

                List<Site> sites;
                if (CurrentUser.Role == UserRoles.Admin)
                {
                    // Admin tüm siteleri görebilir
                    sites = await _firebaseService.GetAllSitesAsync();
                }
                else
                {
                    // Diğer kullanıcılar sadece yetkili oldukları siteleri görebilir
                    sites = await _firebaseService.GetSitesByOwnerAsync(CurrentUser.Id);
                }

                foreach (var site in sites)
                {
                    UserSites.Add(site);
                }

                // İlk siteyi seç
                if (UserSites.Count > 0)
                {
                    SelectedSite = UserSites[0];
                }
            }
            catch (Exception ex)
            {
                // Log error
                System.Diagnostics.Debug.WriteLine($"Error loading user sites: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task LoadSiteApartmentsAsync()
        {
            try
            {
                SiteApartments.Clear();

                if (SelectedSite == null) return;

                var apartments = await _firebaseService.GetApartmentsBySiteAsync(SelectedSite.Id);
                foreach (var apartment in apartments)
                {
                    SiteApartments.Add(apartment);
                }

                // İlk apartmanı seç
                if (SiteApartments.Count > 0)
                {
                    SelectedApartment = SiteApartments[0];
                }
            }
            catch (Exception ex)
            {
                // Log error
                System.Diagnostics.Debug.WriteLine($"Error loading site apartments: {ex.Message}");
            }
        }

        private async Task RefreshDataAsync()
        {
            await LoadUserSitesAsync();
        }

        private void Logout()
        {
            _authService.Logout();
            CurrentUser = null;
            SelectedSite = null;
            SelectedApartment = null;
            UserSites.Clear();
            SiteApartments.Clear();
            LogoutRequested?.Invoke(this, EventArgs.Empty);
        }

        public bool CanAccessFeature(string feature)
        {
            if (CurrentUser == null) return false;

            return feature switch
            {
                "CreateSite" => CurrentUser.Role == UserRoles.Admin || CurrentUser.Role == UserRoles.Manager,
                "DeleteSite" => CurrentUser.Role == UserRoles.Admin,
                "ManageUsers" => CurrentUser.Role == UserRoles.Admin,
                "ViewReports" => true, // Tüm kullanıcılar rapor görüntüleyebilir
                "ManagePayments" => CurrentUser.Role != UserRoles.Viewer,
                _ => true
            };
        }

        public async Task<NotificationSummary?> GetNotificationSummaryAsync()
        {
            if (SelectedSite == null) return null;

            try
            {
                return await _notificationService.GetNotificationSummaryAsync(SelectedSite.Id);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting notification summary: {ex.Message}");
                return null;
            }
        }
    }
}