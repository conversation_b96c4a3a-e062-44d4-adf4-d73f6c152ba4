<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UseWPF>true</UseWPF>
    <StartupObject>ApartmanYonetimSistemi.Program</StartupObject>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="EPPlus" Version="7.5.1" />
    <PackageReference Include="FirebaseAuthentication.net" Version="4.1.0" />
    <PackageReference Include="Google.Cloud.Firestore" Version="3.10.0" />
    <PackageReference Include="iTextSharp.LGPLv2.Core" Version="3.4.22" />
    <PackageReference Include="MailKit" Version="4.8.0" />
    <PackageReference Include="MaterialDesignThemes" Version="5.1.0" />
    <PackageReference Include="Microsoft.Xaml.Behaviors.Wpf" Version="1.1.135" />
  </ItemGroup>

</Project>
