using Google.Cloud.Firestore;
using System.ComponentModel.DataAnnotations;

namespace ApartmanYonetimSistemi.Models
{
    [FirestoreData]
    public class Payment
    {
        [FirestoreProperty("id")]
        public string Id { get; set; } = string.Empty;

        [FirestoreProperty("flatId")]
        [Required(ErrorMessage = "Daire ID zorunludur")]
        public string FlatId { get; set; } = string.Empty;

        [FirestoreProperty("tenantId")]
        public string TenantId { get; set; } = string.Empty;

        [FirestoreProperty("apartmentId")]
        public string ApartmentId { get; set; } = string.Empty;

        [FirestoreProperty("siteId")]
        public string SiteId { get; set; } = string.Empty;

        [FirestoreProperty("amount")]
        [Required(ErrorMessage = "Tutar zorunludur")]
        [Range(0.01, double.MaxValue, ErrorMessage = "Tutar 0'dan b<PERSON>yük olmalıdır")]
        public decimal Amount { get; set; }

        [FirestoreProperty("type")]
        [Required(ErrorMessage = "Ödeme tipi zorunludur")]
        public string Type { get; set; } = string.Empty; // Rent/Dues

        [FirestoreProperty("dueDate")]
        [Required(ErrorMessage = "Vade tarihi zorunludur")]
        public DateTime DueDate { get; set; }

        [FirestoreProperty("paidDate")]
        public DateTime? PaidDate { get; set; }

        [FirestoreProperty("status")]
        [Required(ErrorMessage = "Durum zorunludur")]
        public string Status { get; set; } = "Due"; // Paid/Due/Overdue

        [FirestoreProperty("description")]
        [StringLength(500, ErrorMessage = "Açıklama en fazla 500 karakter olabilir")]
        public string Description { get; set; } = string.Empty;

        [FirestoreProperty("paymentMethod")]
        public string PaymentMethod { get; set; } = string.Empty; // Cash/BankTransfer/CreditCard

        [FirestoreProperty("receiptNumber")]
        public string ReceiptNumber { get; set; } = string.Empty;

        [FirestoreProperty("createdDate")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        [FirestoreProperty("createdBy")]
        public string CreatedBy { get; set; } = string.Empty;

        // Computed properties
        public bool IsOverdue => Status == "Due" && DueDate < DateTime.Now;
        public int DaysOverdue => IsOverdue ? (DateTime.Now - DueDate).Days : 0;
    }

    public static class PaymentTypes
    {
        public const string Rent = "Rent";
        public const string Dues = "Dues";
        public const string Maintenance = "Maintenance";
        public const string Other = "Other";
    }

    public static class PaymentStatus
    {
        public const string Paid = "Paid";
        public const string Due = "Due";
        public const string Overdue = "Overdue";
        public const string Cancelled = "Cancelled";
    }

    public static class PaymentMethods
    {
        public const string Cash = "Cash";
        public const string BankTransfer = "BankTransfer";
        public const string CreditCard = "CreditCard";
        public const string Check = "Check";
    }
}