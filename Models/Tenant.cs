using Google.Cloud.Firestore;
using System.ComponentModel.DataAnnotations;

namespace ApartmanYonetimSistemi.Models
{
    [FirestoreData]
    public class Tenant
    {
        [FirestoreProperty("id")]
        public string Id { get; set; } = string.Empty;

        [FirestoreProperty("name")]
        [Required(ErrorMessage = "Ad zorunludur")]
        [StringLength(50, ErrorMessage = "Ad en fazla 50 karakter olabilir")]
        public string Name { get; set; } = string.Empty;

        [FirestoreProperty("surname")]
        [Required(ErrorMessage = "Soyad zorunludur")]
        [StringLength(50, ErrorMessage = "Soyad en fazla 50 karakter olabilir")]
        public string Surname { get; set; } = string.Empty;

        [FirestoreProperty("phone")]
        [Required(ErrorMessage = "Telefon numarası zorunludur")]
        [Phone(ErrorMessage = "Geçerli bir telefon numarası giriniz")]
        public string Phone { get; set; } = string.Empty;

        [FirestoreProperty("email")]
        [Required(ErrorMessage = "E-posta adresi zorunludur")]
        [EmailAddress(ErrorMessage = "Geçerli bir e-posta adresi giriniz")]
        public string Email { get; set; } = string.Empty;

        [FirestoreProperty("flatId")]
        [Required(ErrorMessage = "Daire ID zorunludur")]
        public string FlatId { get; set; } = string.Empty;

        [FirestoreProperty("apartmentId")]
        public string ApartmentId { get; set; } = string.Empty;

        [FirestoreProperty("siteId")]
        public string SiteId { get; set; } = string.Empty;

        [FirestoreProperty("entryDate")]
        [Required(ErrorMessage = "Giriş tarihi zorunludur")]
        public DateTime EntryDate { get; set; } = DateTime.Now;

        [FirestoreProperty("exitDate")]
        public DateTime? ExitDate { get; set; }

        [FirestoreProperty("identityNumber")]
        [StringLength(11, MinimumLength = 11, ErrorMessage = "TC Kimlik numarası 11 haneli olmalıdır")]
        public string IdentityNumber { get; set; } = string.Empty;

        [FirestoreProperty("emergencyContact")]
        public string EmergencyContact { get; set; } = string.Empty;

        [FirestoreProperty("emergencyPhone")]
        public string EmergencyPhone { get; set; } = string.Empty;

        [FirestoreProperty("notes")]
        [StringLength(1000, ErrorMessage = "Notlar en fazla 1000 karakter olabilir")]
        public string Notes { get; set; } = string.Empty;

        [FirestoreProperty("isActive")]
        public bool IsActive { get; set; } = true;

        [FirestoreProperty("createdDate")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        [FirestoreProperty("createdById")]
        public string CreatedById { get; set; } = string.Empty;

        [FirestoreProperty("lastUpdatedById")]
        public string LastUpdatedById { get; set; } = string.Empty;

        // Computed property
        public string FullName => $"{Name} {Surname}";

        public Tenant Clone()
        {
            return (Tenant)this.MemberwiseClone();
        }
    }
}