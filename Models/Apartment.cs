using Google.Cloud.Firestore;
using System.ComponentModel.DataAnnotations;

namespace ApartmanYonetimSistemi.Models
{
    [FirestoreData]
    public class Apartment
    {
        [FirestoreProperty("id")]
        public string Id { get; set; } = string.Empty;

        [FirestoreProperty("apartmentName")]
        [Required(ErrorMessage = "Apartman adı zorunludur")]
        [StringLength(100, ErrorMessage = "Apartman adı en fazla 100 karakter olabilir")]
        public string ApartmentName { get; set; } = string.Empty;

        [FirestoreProperty("siteId")]
        [Required(ErrorMessage = "Site ID zorunludur")]
        public string SiteId { get; set; } = string.Empty;

        [FirestoreProperty("address")]
        public string Address { get; set; } = string.Empty;

        [FirestoreProperty("totalFlats")]
        public int TotalFlats { get; set; } = 0;

        [FirestoreProperty("createdDate")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        [FirestoreProperty("isActive")]
        public bool IsActive { get; set; } = true;

        [FirestoreProperty("ownerId")]
        public string OwnerId { get; set; } = string.Empty;

        // Navigation property - Firestore'da ayrı collection olarak tutulacak
        public List<Flat> Flats { get; set; } = new List<Flat>();

        public string Status => IsActive ? "Aktif" : "Pasif";

        public Apartment Clone()
        {
            return (Apartment)this.MemberwiseClone();
        }
    }
}