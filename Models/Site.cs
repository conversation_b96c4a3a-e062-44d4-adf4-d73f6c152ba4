using Google.Cloud.Firestore;
using System.ComponentModel.DataAnnotations;

namespace ApartmanYonetimSistemi.Models
{
    [FirestoreData]
    public class Site
    {
        [FirestoreProperty("id")]
        public string Id { get; set; } = string.Empty;

        [FirestoreProperty("siteName")]
        [Required(ErrorMessage = "Site adı zorunludur")]
        [StringLength(100, ErrorMessage = "Site adı en fazla 100 karakter olabilir")]
        public string SiteName { get; set; } = string.Empty;

        [FirestoreProperty("address")]
        [Required(ErrorMessage = "Adres zorunludur")]
        [StringLength(500, ErrorMessage = "Adres en fazla 500 karakter olabilir")]
        public string Address { get; set; } = string.Empty;

        [FirestoreProperty("ownerId")]
        public string OwnerId { get; set; } = string.Empty;

        [FirestoreProperty("createdDate")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        [FirestoreProperty("isActive")]
        public bool IsActive { get; set; } = true;

        // Navigation property - Firestore'da ayrı collection olarak tutulacak
        public List<Apartment> Apartments { get; set; } = new List<Apartment>();

        public string Status => IsActive ? "Aktif" : "Pasif";

        public Site Clone()
        {
            return (Site)this.MemberwiseClone();
        }
    }
}