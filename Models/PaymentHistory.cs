using Google.Cloud.Firestore;
using System.ComponentModel.DataAnnotations;

namespace ApartmanYonetimSistemi.Models
{
    [FirestoreData]
    public class PaymentHistory
    {
        [FirestoreProperty("id")]
        public string Id { get; set; } = string.Empty;

        [FirestoreProperty("paymentId")]
        [Required(ErrorMessage = "Ödeme ID zorunludur")]
        public string PaymentId { get; set; } = string.Empty;

        [FirestoreProperty("flatId")]
        public string FlatId { get; set; } = string.Empty;

        [FirestoreProperty("tenantId")]
        public string TenantId { get; set; } = string.Empty;

        [FirestoreProperty("apartmentId")]
        public string ApartmentId { get; set; } = string.Empty;

        [FirestoreProperty("siteId")]
        public string SiteId { get; set; } = string.Empty;

        [FirestoreProperty("transactionDate")]
        [Required(ErrorMessage = "İşlem tarihi zorunludur")]
        public DateTime TransactionDate { get; set; } = DateTime.Now;

        [FirestoreProperty("amount")]
        [Required(ErrorMessage = "Tutar zorunludur")]
        [Range(0.01, double.MaxValue, ErrorMessage = "Tutar 0'dan büyük olmalıdır")]
        public decimal Amount { get; set; }

        [FirestoreProperty("previousStatus")]
        public string PreviousStatus { get; set; } = string.Empty;

        [FirestoreProperty("newStatus")]
        [Required(ErrorMessage = "Yeni durum zorunludur")]
        public string NewStatus { get; set; } = string.Empty;

        [FirestoreProperty("paymentMethod")]
        public string PaymentMethod { get; set; } = string.Empty;

        [FirestoreProperty("receiptNumber")]
        public string ReceiptNumber { get; set; } = string.Empty;

        [FirestoreProperty("notes")]
        [StringLength(1000, ErrorMessage = "Notlar en fazla 1000 karakter olabilir")]
        public string Notes { get; set; } = string.Empty;

        [FirestoreProperty("processedBy")]
        [Required(ErrorMessage = "İşlemi yapan kişi zorunludur")]
        public string ProcessedBy { get; set; } = string.Empty;

        [FirestoreProperty("createdDate")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        // Computed property
        public string TransactionDescription => $"{PreviousStatus} -> {NewStatus}";
    }
}