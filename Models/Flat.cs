using Google.Cloud.Firestore;
using System.ComponentModel.DataAnnotations;

namespace ApartmanYonetimSistemi.Models
{
    [FirestoreData]
    public class Flat
    {
        [FirestoreProperty("id")]
        public string Id { get; set; } = string.Empty;

        [FirestoreProperty("flatNo")]
        [Required(ErrorMessage = "Daire numarası zorunludur")]
        [StringLength(10, ErrorMessage = "Daire numarası en fazla 10 karakter olabilir")]
        public string FlatNo { get; set; } = string.Empty;

        [FirestoreProperty("apartmentId")]
        [Required(ErrorMessage = "Apartman ID zorunludur")]
        public string ApartmentId { get; set; } = string.Empty;

        [FirestoreProperty("siteId")]
        [Required(ErrorMessage = "Site ID zorunludur")]
        public string SiteId { get; set; } = string.Empty;

        [FirestoreProperty("rentAmount")]
        [Range(0, double.MaxValue, ErrorMessage = "<PERSON> bedeli 0'dan büyük olmalıdır")]
        public decimal RentAmount { get; set; }

        [FirestoreProperty("rentStatus")]
        public string RentStatus { get; set; } = "Due"; // Paid/Due

        [FirestoreProperty("dues")]
        [Range(0, double.MaxValue, ErrorMessage = "Aidat 0'dan büyük olmalıdır")]
        public decimal Dues { get; set; }

        [FirestoreProperty("duesStatus")]
        public string DuesStatus { get; set; } = string.Empty; // "Paid", "Due"

        [FirestoreProperty("rentDueDate")]
        public DateTime RentDueDate { get; set; } = DateTime.Now.AddMonths(1);

        [FirestoreProperty("duesDueDate")]
        public DateTime DuesDueDate { get; set; } = DateTime.Now.AddMonths(1);

        [FirestoreProperty("isOccupied")]
        public bool IsOccupied { get; set; } = false;

        [FirestoreProperty("createdDate")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        [FirestoreProperty("isActive")]
        public bool IsActive { get; set; } = true;

        [FirestoreProperty("createdById")]
        public string CreatedById { get; set; } = string.Empty;

        // Navigation property
        public Tenant? Tenant { get; set; }

        public Flat Clone()
        {
            return (Flat)this.MemberwiseClone();
        }
    }
}