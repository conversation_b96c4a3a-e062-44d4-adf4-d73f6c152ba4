using Google.Cloud.Firestore;
using System.ComponentModel.DataAnnotations;

namespace ApartmanYonetimSistemi.Models
{
    [FirestoreData]
    public class User
    {
        [FirestoreProperty("id")]
        public string Id { get; set; } = string.Empty;

        [FirestoreProperty("name")]
        [Required(ErrorMessage = "Ad zorunludur")]
        [StringLength(100, ErrorMessage = "Ad en fazla 100 karakter olabilir")]
        public string Name { get; set; } = string.Empty;

        [FirestoreProperty("email")]
        [Required(ErrorMessage = "E-posta adresi zorunludur")]
        [EmailAddress(ErrorMessage = "Geçerli bir e-posta adresi giriniz")]
        public string Email { get; set; } = string.Empty;

        [FirestoreProperty("role")]
        [Required(ErrorMessage = "Rol zorunludur")]
        public string Role { get; set; } = "Viewer"; // Ad<PERSON>, Manager, Viewer

        [FirestoreProperty("siteIds")]
        public List<string> SiteIds { get; set; } = new List<string>();

        [FirestoreProperty("phone")]
        [Phone(ErrorMessage = "Geçerli bir telefon numarası giriniz")]
        public string Phone { get; set; } = string.Empty;

        [FirestoreProperty("company")]
        [StringLength(200, ErrorMessage = "Şirket adı en fazla 200 karakter olabilir")]
        public string Company { get; set; } = string.Empty;

        [FirestoreProperty("isActive")]
        public bool IsActive { get; set; } = true;

        [FirestoreProperty("createdDate")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        [FirestoreProperty("lastLoginDate")]
        public DateTime? LastLoginDate { get; set; }

        // Firebase Auth UID
        [FirestoreProperty("firebaseUid")]
        public string FirebaseUid { get; set; } = string.Empty;
    }

    public static class UserRoles
    {
        public const string Admin = "Admin";
        public const string Manager = "Manager";
        public const string Viewer = "Viewer";
    }
}