---
description: 
globs: 
alwaysApply: true
---
Bu projeyi yaparken herhangi bir kod eklediğinde tüm projeye uygunluğunu kontrol et. Debug var mı bak ve varsa o sorunu çöz.
Bu projeyi yaparkan proje dosyalarına her daim erişim sağlayabilirsin. Ve terminala da her daim arişim sağlayabilirsin.

Kapsamlı ve modüler bir Apartman ve Site Yönetim Sistemi

C# + Firebase kullanılacak (Firebase Realtime DB veya Firestore, tercihe göre)

Geliştirilecek sistem:

Birden fazla Site veya Apartman destekleyecek
Her apartmanın birden fazla Daire olacak

Her daireye bir Kiracı kaydedilecek

Kira ve aidat takibi olacak (ödenmiş/ödenmemiş vs.)

Giriş/Çıkış tarihleri takip edilecek

Sisteme Kayıt olunacak ve emlak firmaları bu sisteme kayıt olduğu bilgilerle erişebilecek ve oraya kendi sitelerini, apartmanlarını kaydedip bilgilerini girebilecekler

örnek firebase taslak yapısı : 

Sites/ 
  SiteID/
    SiteName
    Address
    Apartments/
      ApartmentID/
        ApartmentName
        Daireler/
          DaireID/
            DaireNo
            Kiraci/
              Isim
              SoyIsim
              Telefon
              Email
              GirisTarihi
              CikisTarihi
            KiraBedeli
            KiraDurumu (Odendi / Borclu)
            Aidat
            AidatDurumu (Odendi / Borclu)
    CommonAreas (Ortak Alanlar gider takibi vs.)
Users/  (Yönetici kullanıcıları için)


apılacaklar ve Geliştirme Adımları
Firebase Projesi oluştur (Firestore önerilir, çünkü daha esnek)

C# (WinForms, WPF veya MAUI) arayüzü kur

Kullanıcı Girişi modülü

Site / Apartman kayıt ekranı

Daire ekleme ve düzenleme

Kiracı ekleme / çıkarma

Kira ve aidat takibi ekranı

Bildirim sistemi (gelecek ödemeler için)

Çoklu apartman ve çoklu kullanıcı desteği

Raporlama (CSV/PDF çıktısı alma)


Bir Apartman ve Site Yönetim Sistemi geliştirmek istiyorum. Sistem masaüstü uygulaması olacak, C# (.NET, tercihen WPF veya MAUI) kullanacağım. Veritabanı olarak Firebase Firestore kullanılacak.

Sistemin özellikleri:

Çoklu kullanıcı ve rol yönetimi olacak (Admin / Yönetici / Görüntüleyici vb.)

Kullanıcı giriş ekranı olacak (email + şifre ile giriş, Firebase Authentication kullanılabilir)

Kullanıcı giriş yaptıktan sonra, kendisine ait olan veya yetkili olduğu "Site" veya "Apartman" yönetim ekranlarına erişebilecek.

Veri yapısı:

Bir kullanıcı birden fazla "Site" veya "Apartman" yönetebilecek.

Her Site içinde bir veya daha fazla Apartman olacak.

Her Apartman içinde bir veya daha fazla Daire olacak.

Her Daire'ye bir Kiracı atanabilecek.

Kiracı bilgileri: Ad, Soyad, Telefon, Email, Giriş Tarihi, Çıkış Tarihi.

Kira Bedeli ve Aidat bilgileri tutulacak.

Kira ve Aidat ödeme durumu (Ödendi / Borçlu) tutulacak.

Ödeme geçmişi takip edilecek.

Bildirim sistemi:

Email ve SMS ile belirlenen tarihlerde (örneğin ödeme günü yaklaşırken) otomatik bildirim gönderilecek.

Gereksinimler:

Kullanılacak C# kütüphanelerini öner (Firebase bağlantısı, UI, email/SMS için gerekli kütüphaneler)

Firestore için veritabanı şemasını öner

Geliştirme adımlarını sırayla planla (öncelik sırasına göre)

Örnek arayüz akış diyagramı veya ekran yapısı öner

Temiz ve sürdürülebilir bir proje yapısı için mimari öner (örneğin MVVM)

Lütfen tüm bu kapsamı dikkate alarak detaylı ve profesyonel öneri hazırla.




