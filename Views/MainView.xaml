<UserControl x:Class="ApartmanYonetimSistemi.Views.MainView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:ApartmanYonetimSistemi.Views"
             xmlns:views="clr-namespace:ApartmanYonetimSistemi.Views"
             xmlns:viewModels="clr-namespace:ApartmanYonetimSistemi.ViewModels"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:behaviors="http://schemas.microsoft.com/xaml/behaviors"
             xmlns:resources="clr-namespace:ApartmanYonetimSistemi.Resources"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="1000">

    <UserControl.Resources>
        <resources:StringToVisibilityConverter x:Key="StringToVisibilityConverter"/>
        <resources:BoolToVisibilityConverter x:Key="BoolToVisibilityConverter"/>
        <resources:InverseBoolToVisibilityConverter x:Key="InverseBoolToVisibilityConverter"/>
        <resources:NotNullToBoolConverter x:Key="NotNullToBoolConverter"/>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Top Bar -->
        <materialDesign:Card Grid.Row="0" Margin="0,0,0,8">
            <Grid Margin="16,8">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Logo and Title -->
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="Building" Width="32" Height="32"
                                           Foreground="{DynamicResource PrimaryHueMidBrush}"
                                           VerticalAlignment="Center"/>
                    <TextBlock Text="Apartman Yönetim Sistemi"
                             Style="{DynamicResource MaterialDesignHeadline6TextBlock}"
                             VerticalAlignment="Center" Margin="8,0,0,0"/>
                </StackPanel>

                <!-- Site Selection -->
                <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Center">
                    <TextBlock Text="Site:" VerticalAlignment="Center" Margin="0,0,8,0"/>
                    <ComboBox ItemsSource="{Binding UserSites}"
                            SelectedItem="{Binding SelectedSite}"
                            DisplayMemberPath="SiteName"
                            MinWidth="200"
                            materialDesign:HintAssist.Hint="Site Seçin"/>
                </StackPanel>

                <!-- User Info and Logout -->
                <StackPanel Grid.Column="2" Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="Account" Width="24" Height="24"
                                           VerticalAlignment="Center" Margin="0,0,8,0"/>
                    <TextBlock Text="{Binding CurrentUser.Name}"
                             VerticalAlignment="Center" Margin="0,0,16,0"/>
                    <Button Content="Çıkış"
                          Command="{Binding LogoutCommand}"
                          Style="{DynamicResource MaterialDesignFlatButton}"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Main Content -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="250"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Navigation Menu -->
            <materialDesign:Card Grid.Column="0" Margin="0,0,8,0">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="8">
                        <TextBlock Text="Menü"
                                 Style="{DynamicResource MaterialDesignSubtitle1TextBlock}"
                                 Margin="8,8,8,16"/>

                        <Button Content="Dashboard"
                              Command="{Binding ShowDashboardCommand}"
                              Style="{DynamicResource MaterialDesignFlatButton}"
                              HorizontalAlignment="Stretch"
                              HorizontalContentAlignment="Left"
                              Margin="0,2"/>

                        <Button Content="Siteler"
                              Command="{Binding ShowSitesCommand}"
                              Style="{DynamicResource MaterialDesignFlatButton}"
                              HorizontalAlignment="Stretch"
                              HorizontalContentAlignment="Left"
                              Margin="0,2"/>

                        <Button Content="Apartmanlar"
                              Command="{Binding ShowApartmentsCommand}"
                              Style="{DynamicResource MaterialDesignFlatButton}"
                              HorizontalAlignment="Stretch"
                              HorizontalContentAlignment="Left"
                              Margin="0,2"/>

                        <Button Content="Daireler"
                              Command="{Binding ShowFlatsCommand}"
                              Style="{DynamicResource MaterialDesignFlatButton}"
                              HorizontalAlignment="Stretch"
                              HorizontalContentAlignment="Left"
                              Margin="0,2"/>

                        <Button Content="Kiracılar"
                              Command="{Binding ShowTenantsCommand}"
                              Style="{DynamicResource MaterialDesignFlatButton}"
                              HorizontalAlignment="Stretch"
                              HorizontalContentAlignment="Left"
                              Margin="0,2"/>

                        <Button Content="Ödemeler"
                              Command="{Binding ShowPaymentsCommand}"
                              Style="{DynamicResource MaterialDesignFlatButton}"
                              HorizontalAlignment="Stretch"
                              HorizontalContentAlignment="Left"
                              Margin="0,2"/>

                        <Button Content="Raporlar"
                              Command="{Binding ShowReportsCommand}"
                              Style="{DynamicResource MaterialDesignFlatButton}"
                              HorizontalAlignment="Stretch"
                              HorizontalContentAlignment="Left"
                              Margin="0,2"/>

                        <Button Content="Ayarlar"
                              Command="{Binding ShowSettingsCommand}"
                              Style="{DynamicResource MaterialDesignFlatButton}"
                              HorizontalAlignment="Stretch"
                              HorizontalContentAlignment="Left"
                              Margin="0,2"/>
                    </StackPanel>
                </ScrollViewer>
            </materialDesign:Card>

            <!-- Content Area -->
            <materialDesign:Card Grid.Column="1">
                <Grid Margin="16">
                    <!-- Dashboard -->
                    <StackPanel Visibility="{Binding CurrentView, Converter={StaticResource StringToVisibilityConverter}, ConverterParameter=Dashboard}">
                        <TextBlock Text="Dashboard"
                                 Style="{DynamicResource MaterialDesignHeadline5TextBlock}"
                                 Margin="0,0,0,16"/>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <materialDesign:Card Grid.Column="0" Margin="0,0,8,0" Background="{DynamicResource PrimaryHueMidBrush}">
                                <StackPanel Margin="16" HorizontalAlignment="Center">
                                    <materialDesign:PackIcon Kind="Building" Width="32" Height="32"
                                                           Foreground="White" HorizontalAlignment="Center"/>
                                    <TextBlock Text="Toplam Site" Foreground="White" HorizontalAlignment="Center" Margin="0,8,0,0"/>
                                    <TextBlock Text="{Binding UserSites.Count}"
                                             Foreground="White"
                                             FontSize="24"
                                             FontWeight="Bold"
                                             HorizontalAlignment="Center"/>
                                </StackPanel>
                            </materialDesign:Card>

                            <materialDesign:Card Grid.Column="1" Margin="4,0" Background="{DynamicResource SecondaryHueMidBrush}">
                                <StackPanel Margin="16" HorizontalAlignment="Center">
                                    <materialDesign:PackIcon Kind="Home" Width="32" Height="32"
                                                           Foreground="White" HorizontalAlignment="Center"/>
                                    <TextBlock Text="Apartmanlar" Foreground="White" HorizontalAlignment="Center" Margin="0,8,0,0"/>
                                    <TextBlock Text="{Binding SiteApartments.Count}"
                                             Foreground="White"
                                             FontSize="24"
                                             FontWeight="Bold"
                                             HorizontalAlignment="Center"/>
                                </StackPanel>
                            </materialDesign:Card>

                            <materialDesign:Card Grid.Column="2" Margin="8,0,0,0" Background="#FF9800">
                                <StackPanel Margin="16" HorizontalAlignment="Center">
                                    <materialDesign:PackIcon Kind="CurrencyUsd" Width="32" Height="32"
                                                           Foreground="White" HorizontalAlignment="Center"/>
                                    <TextBlock Text="Bu Ay" Foreground="White" HorizontalAlignment="Center" Margin="0,8,0,0"/>
                                    <TextBlock Text="₺0"
                                             Foreground="White"
                                             FontSize="24"
                                             FontWeight="Bold"
                                             HorizontalAlignment="Center"/>
                                </StackPanel>
                            </materialDesign:Card>
                        </Grid>

                        <TextBlock Text="Hoş geldiniz! Lütfen sol menüden işlem yapmak istediğiniz bölümü seçin."
                                 Style="{DynamicResource MaterialDesignBody1TextBlock}"
                                 Margin="0,32,0,0"
                                 TextWrapping="Wrap"/>
                    </StackPanel>

                    <!-- Sites View -->
                    <views:SiteView x:Name="SiteView"
                                    DataContext="{Binding SiteViewModel}"
                                    Visibility="{Binding CurrentView, Converter={StaticResource StringToVisibilityConverter}, ConverterParameter=Sites}"/>

                    <!-- Apartments View -->
                    <views:ApartmentView x:Name="ApartmentView"
                                         DataContext="{Binding ApartmentViewModel}"
                                         Visibility="{Binding CurrentView, Converter={StaticResource StringToVisibilityConverter}, ConverterParameter=Apartments}"/>

                    <!-- Flats View -->
                    <views:FlatView x:Name="FlatView"
                                    DataContext="{Binding FlatViewModel}"
                                    Visibility="{Binding CurrentView, Converter={StaticResource StringToVisibilityConverter}, ConverterParameter=Flats}"/>

                    <!-- Tenants View -->
                    <views:TenantView DataContext="{Binding TenantViewModel}"
                                      Visibility="{Binding CurrentView, Converter={StaticResource StringToVisibilityConverter}, ConverterParameter=Tenants}"/>

                    <!-- Payments View -->
                    <views:PaymentView DataContext="{Binding PaymentViewModel}"
                                       Visibility="{Binding CurrentView, Converter={StaticResource StringToVisibilityConverter}, ConverterParameter=Payments}"/>

                    <!-- Reports View -->
                    <views:ReportView DataContext="{Binding ReportViewModel}"
                                      Visibility="{Binding CurrentView, Converter={StaticResource StringToVisibilityConverter}, ConverterParameter=Reports}"/>

                    <!-- Settings View -->
                    <StackPanel Visibility="{Binding CurrentView, Converter={StaticResource StringToVisibilityConverter}, ConverterParameter=Settings}">
                        <TextBlock Text="Ayarlar"
                                 Style="{DynamicResource MaterialDesignHeadline5TextBlock}"
                                 Margin="0,0,0,16"/>
                        <TextBlock Text="Ayarlar modülü yakında eklenecek..."
                                 Style="{DynamicResource MaterialDesignBody1TextBlock}"/>
                    </StackPanel>
                </Grid>
            </materialDesign:Card>
        </Grid>

        <!-- Loading Overlay -->
        <Grid Grid.RowSpan="2"
              Background="#80000000"
              Visibility="{Binding IsLoading, Converter={StaticResource BoolToVisibilityConverter}}">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <ProgressBar IsIndeterminate="True" Width="100" Height="100"
                           Style="{DynamicResource MaterialDesignCircularProgressBar}"/>
                <TextBlock Text="Yükleniyor..."
                         Foreground="White"
                         HorizontalAlignment="Center"
                         Margin="0,16,0,0"/>
            </StackPanel>
        </Grid>
    </Grid>
</UserControl>