<UserControl x:Class="ApartmanYonetimSistemi.Views.SiteView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:vm="clr-namespace:ApartmanYonetimSistemi.ViewModels"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:converters="clr-namespace:ApartmanYonetimSistemi.Resources"
             xmlns:i="http://schemas.microsoft.com/xaml/behaviors"
             mc:Ignorable="d"
             d:DesignHeight="720" d:DesignWidth="1280">
    <i:Interaction.Triggers>
        <i:EventTrigger EventName="Loaded">
            <i:InvokeCommandAction Command="{Binding LoadSitesCommand}"/>
        </i:EventTrigger>
    </i:Interaction.Triggers>

    <UserControl.Resources>
        <converters:StringToVisibilityConverter x:Key="StringToVisibilityConverter"/>
        <converters:BoolToVisibilityConverter x:Key="BoolToVisibilityConverter"/>
        <converters:InverseBoolToVisibilityConverter x:Key="InverseBoolToVisibilityConverter"/>
        <converters:BoolToStringConverter x:Key="BoolToStringConverter"/>
        <converters:NotNullToBoolConverter x:Key="NotNullToBoolConverter"/>
    </UserControl.Resources>

    <UserControl.DataContext>
        <vm:SiteViewModel/>
    </UserControl.DataContext>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Modern Header -->
        <materialDesign:Card Grid.Row="0" Margin="0,0,0,16">
            <Grid Margin="24,16">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" VerticalAlignment="Center">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Domain" Width="32" Height="32"
                                               Foreground="{DynamicResource PrimaryHueMidBrush}"
                                               VerticalAlignment="Center" Margin="0,0,12,0"/>
                        <TextBlock Text="Site Yönetimi"
                                 Style="{DynamicResource MaterialDesignHeadline5TextBlock}"
                                 VerticalAlignment="Center"/>
                    </StackPanel>
                    <TextBlock Text="Siteleri görüntüleyin, ekleyin ve düzenleyin"
                             Style="{DynamicResource MaterialDesignBody2TextBlock}"
                             Foreground="{DynamicResource MaterialDesignBodyLight}"
                             Margin="44,4,0,0"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                    <Button Command="{Binding LoadSitesCommand}"
                            Style="{DynamicResource MaterialDesignOutlinedButton}"
                            Margin="0,0,8,0" ToolTip="Verileri Yenile">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Refresh" Width="18" Height="18" Margin="0,0,8,0"/>
                            <TextBlock Text="Yenile"/>
                        </StackPanel>
                    </Button>
                    <Button Command="{Binding NewSiteCommand}"
                            Style="{DynamicResource MaterialDesignRaisedButton}"
                            ToolTip="Yeni Site Ekle">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Plus" Width="18" Height="18" Margin="0,0,8,0"/>
                            <TextBlock Text="Yeni Site"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Modern Content Area -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="2*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Sites List Card -->
            <materialDesign:Card Grid.Column="0" Margin="0,0,8,0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- List Header -->
                    <Border Grid.Row="0" Background="{DynamicResource MaterialDesignPaper}" Padding="24,16">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="FormatListBulleted" Width="24" Height="24"
                                                   Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                   VerticalAlignment="Center" Margin="0,0,12,0"/>
                            <TextBlock Text="Site Listesi"
                                     Style="{DynamicResource MaterialDesignHeadline6TextBlock}"
                                     VerticalAlignment="Center"/>
                            <TextBlock Text="{Binding Sites.Count, StringFormat='({0} site)'}"
                                     Style="{DynamicResource MaterialDesignBody2TextBlock}"
                                     Foreground="{DynamicResource MaterialDesignBodyLight}"
                                     VerticalAlignment="Center" Margin="8,0,0,0"/>
                        </StackPanel>
                    </Border>

                    <!-- Modern DataGrid -->
                    <DataGrid Grid.Row="1" ItemsSource="{Binding Sites}" SelectedItem="{Binding SelectedSite}"
                            AutoGenerateColumns="False" CanUserAddRows="False" CanUserDeleteRows="False"
                            IsReadOnly="True" GridLinesVisibility="Horizontal" HeadersVisibility="Column"
                            materialDesign:DataGridAssist.CellPadding="16,8"
                            materialDesign:DataGridAssist.ColumnHeaderPadding="16,12"
                            Margin="0,0,0,8">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="Site Adı" Binding="{Binding SiteName}" Width="*" MinWidth="150">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="FontWeight" Value="Medium"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTextColumn Header="Adres" Binding="{Binding Address}" Width="2*" MinWidth="200">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="TextWrapping" Value="Wrap"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                        <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBodyLight}"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTextColumn Header="Durum" Binding="{Binding Status}" Width="Auto" MinWidth="100">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                        <Setter Property="FontWeight" Value="Medium"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </materialDesign:Card>

            <!-- Site Details Card -->
            <materialDesign:Card Grid.Column="1" Margin="8,0,0,0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- Details Header -->
                    <Border Grid.Row="0" Background="{DynamicResource MaterialDesignPaper}" Padding="24,16">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="InformationOutline" Width="24" Height="24"
                                                   Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                   VerticalAlignment="Center" Margin="0,0,12,0"/>
                            <TextBlock Text="Site Detayları"
                                     Style="{DynamicResource MaterialDesignHeadline6TextBlock}"
                                     VerticalAlignment="Center"/>
                        </StackPanel>
                    </Border>

                    <!-- Content Area -->
                    <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="24">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <!-- Site Adı -->
                            <Grid Grid.Row="0" Margin="0,0,0,16">
                                <TextBlock Text="{Binding SelectedSite.SiteName}"
                                           Style="{DynamicResource MaterialDesignBody1TextBlock}"
                                           FontWeight="Medium" TextWrapping="Wrap"
                                           VerticalAlignment="Center"
                                           Visibility="{Binding IsEditing, Converter={StaticResource InverseBoolToVisibilityConverter}}"/>
                                <TextBox materialDesign:HintAssist.Hint="Site Adı"
                                         Text="{Binding CurrentSite.SiteName, UpdateSourceTrigger=PropertyChanged}"
                                         Style="{DynamicResource MaterialDesignFloatingHintTextBox}"
                                         VerticalAlignment="Center"
                                         Visibility="{Binding IsEditing, Converter={StaticResource BoolToVisibilityConverter}}"/>
                            </Grid>

                            <!-- Adres -->
                            <Grid Grid.Row="1" Margin="0,0,0,16">
                                <TextBlock Text="{Binding SelectedSite.Address}"
                                           Style="{DynamicResource MaterialDesignBody1TextBlock}"
                                           TextWrapping="Wrap"
                                           VerticalAlignment="Center"
                                           Visibility="{Binding IsEditing, Converter={StaticResource InverseBoolToVisibilityConverter}}"/>
                                <TextBox materialDesign:HintAssist.Hint="Adres"
                                         Text="{Binding CurrentSite.Address, UpdateSourceTrigger=PropertyChanged}"
                                         Style="{DynamicResource MaterialDesignFloatingHintTextBox}"
                                         AcceptsReturn="True" TextWrapping="Wrap" MinLines="3"
                                         VerticalAlignment="Center"
                                         Visibility="{Binding IsEditing, Converter={StaticResource BoolToVisibilityConverter}}"/>
                            </Grid>

                            <!-- Durum -->
                            <Grid Grid.Row="2" Margin="0,0,0,16">
                                <StackPanel Orientation="Horizontal"
                                            Visibility="{Binding IsEditing, Converter={StaticResource InverseBoolToVisibilityConverter}}">
                                    <materialDesign:PackIcon Kind="CheckCircle" Width="20" Height="20"
                                                           Foreground="{DynamicResource SecondaryHueMidBrush}"
                                                           VerticalAlignment="Center" Margin="0,0,8,0"/>
                                    <TextBlock Text="{Binding SelectedSite.Status}"
                                             Style="{DynamicResource MaterialDesignBody1TextBlock}"
                                             FontWeight="Medium" VerticalAlignment="Center"/>
                                </StackPanel>

                                <CheckBox Content="Aktif"
                                          IsChecked="{Binding CurrentSite.IsActive}"
                                          Style="{DynamicResource MaterialDesignCheckBox}"
                                          VerticalAlignment="Center"
                                          Visibility="{Binding IsEditing, Converter={StaticResource BoolToVisibilityConverter}}"/>
                            </Grid>
                        </Grid>
                    </ScrollViewer>

                    <!-- Action Buttons -->
                    <Border Grid.Row="2" Background="{DynamicResource MaterialDesignCardBackground}" Padding="24,16">
                        <StackPanel>
                            <!-- Edit Mode Buttons -->
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right"
                                      Visibility="{Binding IsEditing, Converter={StaticResource BoolToVisibilityConverter}}">
                                <Button Command="{Binding CancelEditCommand}"
                                      Style="{DynamicResource MaterialDesignOutlinedButton}"
                                      Margin="0,0,8,0">
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="Close" Width="18" Height="18" Margin="0,0,8,0"/>
                                        <TextBlock Text="İptal"/>
                                    </StackPanel>
                                </Button>
                                <Button Command="{Binding SaveSiteCommand}"
                                      Style="{DynamicResource MaterialDesignRaisedButton}">
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="ContentSave" Width="18" Height="18" Margin="0,0,8,0"/>
                                        <TextBlock Text="Kaydet"/>
                                    </StackPanel>
                                </Button>
                            </StackPanel>

                            <!-- View Mode Buttons -->
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right"
                                      Visibility="{Binding IsEditing, Converter={StaticResource InverseBoolToVisibilityConverter}}">
                                <Button Command="{Binding EditSiteCommand}"
                                      Style="{DynamicResource MaterialDesignOutlinedButton}"
                                      Margin="0,0,8,0"
                                      IsEnabled="{Binding SelectedSite, Converter={StaticResource NotNullToBoolConverter}}">
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="Pencil" Width="18" Height="18" Margin="0,0,8,0"/>
                                        <TextBlock Text="Düzenle"/>
                                    </StackPanel>
                                </Button>
                                <Button Command="{Binding DeleteSiteCommand}"
                                      Style="{DynamicResource MaterialDesignOutlinedButton}"
                                      Foreground="{DynamicResource ValidationErrorBrush}"
                                      BorderBrush="{DynamicResource ValidationErrorBrush}"
                                      IsEnabled="{Binding SelectedSite, Converter={StaticResource NotNullToBoolConverter}}">
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="Delete" Width="18" Height="18" Margin="0,0,8,0"/>
                                        <TextBlock Text="Sil"/>
                                    </StackPanel>
                                </Button>
                            </StackPanel>

                            <!-- Messages -->
                            <materialDesign:Card Margin="0,16,0,0"
                                               Background="{DynamicResource ValidationErrorBrush}"
                                               Visibility="{Binding ErrorMessage, Converter={StaticResource StringToVisibilityConverter}}">
                                <StackPanel Orientation="Horizontal" Margin="16">
                                    <materialDesign:PackIcon Kind="AlertCircle" Width="20" Height="20"
                                                           Foreground="White" VerticalAlignment="Center" Margin="0,0,12,0"/>
                                    <TextBlock Text="{Binding ErrorMessage}"
                                             Foreground="White" TextWrapping="Wrap" VerticalAlignment="Center"/>
                                </StackPanel>
                            </materialDesign:Card>

                            <materialDesign:Card Margin="0,16,0,0"
                                               Background="{DynamicResource SecondaryHueMidBrush}"
                                               Visibility="{Binding SuccessMessage, Converter={StaticResource StringToVisibilityConverter}}">
                                <StackPanel Orientation="Horizontal" Margin="16">
                                    <materialDesign:PackIcon Kind="CheckCircle" Width="20" Height="20"
                                                           Foreground="White" VerticalAlignment="Center" Margin="0,0,12,0"/>
                                    <TextBlock Text="{Binding SuccessMessage}"
                                             Foreground="White" TextWrapping="Wrap" VerticalAlignment="Center"/>
                                </StackPanel>
                            </materialDesign:Card>
                        </StackPanel>
                    </Border>
                </Grid>
            </materialDesign:Card>
        </Grid>

        <!-- Modern Loading Overlay -->
        <Grid Grid.RowSpan="2" Background="{DynamicResource MaterialDesignPaper}" Opacity="0.8"
              Visibility="{Binding IsLoading, Converter={StaticResource BoolToVisibilityConverter}}">
            <materialDesign:Card HorizontalAlignment="Center" VerticalAlignment="Center"
                               Padding="32">
                <StackPanel HorizontalAlignment="Center">
                    <ProgressBar Style="{DynamicResource MaterialDesignCircularProgressBar}"
                               IsIndeterminate="True" Width="48" Height="48" Margin="0,0,0,16"/>
                    <TextBlock Text="Yükleniyor..."
                             Style="{DynamicResource MaterialDesignBody1TextBlock}"
                             HorizontalAlignment="Center"/>
                </StackPanel>
            </materialDesign:Card>
        </Grid>
    </Grid>
</UserControl>