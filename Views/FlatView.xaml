<UserControl x:Class="ApartmanYonetimSistemi.Views.FlatView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:vm="clr-namespace:ApartmanYonetimSistemi.ViewModels"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:converters="clr-namespace:ApartmanYonetimSistemi.Resources"
             xmlns:i="http://schemas.microsoft.com/xaml/behaviors"
             mc:Ignorable="d"
             d:DesignHeight="720" d:DesignWidth="1280">
    <i:Interaction.Triggers>
        <i:EventTrigger EventName="Loaded">
            <i:InvokeCommandAction Command="{Binding LoadFlatsCommand}"/>
        </i:EventTrigger>
    </i:Interaction.Triggers>

    <UserControl.Resources>
        <converters:StringToVisibilityConverter x:Key="StringToVisibilityConverter"/>
        <converters:BoolToVisibilityConverter x:Key="BoolToVisibilityConverter"/>
        <converters:InverseBoolToVisibilityConverter x:Key="InverseBoolToVisibilityConverter"/>
        <converters:BoolToStringConverter x:Key="BoolToStringConverter"/>
        <converters:NotNullToBoolConverter x:Key="NotNullToBoolConverter"/>
        <converters:InverseBoolConverter x:Key="InverseBoolConverter"/>
    </UserControl.Resources>



    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Modern Header -->
        <materialDesign:Card Grid.Row="0" Margin="0,0,0,16">
            <Grid Margin="24,16">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                <StackPanel Grid.Column="0" VerticalAlignment="Center">
                    <TextBlock Text="Daire Yönetimi" Style="{DynamicResource MaterialDesignHeadline5TextBlock}"/>
                    <TextBlock Text="Apartmanlara ait daireleri yönetin" Style="{DynamicResource MaterialDesignBody2TextBlock}" Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                </StackPanel>
                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                    <Button Command="{Binding LoadFlatsCommand}" Style="{DynamicResource MaterialDesignOutlinedButton}" Margin="0,0,8,0" ToolTip="Yenile">
                        <materialDesign:PackIcon Kind="Refresh" />
                    </Button>
                    <Button Command="{Binding NewFlatCommand}" Style="{DynamicResource MaterialDesignRaisedButton}" ToolTip="Yeni Daire Ekle">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Plus" Margin="0,0,8,0"/>
                            <TextBlock Text="Yeni Daire"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Main Content -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="2*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Daire Listesi -->
            <materialDesign:Card Grid.Column="0" Margin="0,0,8,0">
            <Grid Margin="16">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Başlık -->
                <TextBlock Grid.Row="0" Text="Daireler" Style="{DynamicResource MaterialDesignHeadline6TextBlock}" Margin="0,0,0,16"/>

                <!-- Hata/Başarı Mesajları -->
                <StackPanel Grid.Row="1" Margin="0,0,0,16">
                    <materialDesign:Card Background="{DynamicResource ValidationErrorBrush}"
                                       Visibility="{Binding ErrorMessage, Converter={StaticResource StringToVisibilityConverter}}"
                                       Margin="0,0,0,8">
                        <TextBlock Text="{Binding ErrorMessage}"
                                 Foreground="White"
                                 Margin="16,8"
                                 TextWrapping="Wrap"/>
                    </materialDesign:Card>

                    <materialDesign:Card Background="{DynamicResource PrimaryHueMidBrush}"
                                       Visibility="{Binding SuccessMessage, Converter={StaticResource StringToVisibilityConverter}}"
                                       Margin="0,0,0,8">
                        <TextBlock Text="{Binding SuccessMessage}"
                                 Foreground="White"
                                 Margin="16,8"
                                 TextWrapping="Wrap"/>
                    </materialDesign:Card>
                </StackPanel>

                <!-- Daire Listesi -->
                <DataGrid Grid.Row="2"
                        ItemsSource="{Binding Flats}"
                        SelectedItem="{Binding SelectedFlat, Mode=TwoWay}"
                        AutoGenerateColumns="False"
                        CanUserAddRows="False"
                        CanUserDeleteRows="False"
                        IsReadOnly="True"
                        materialDesign:DataGridAssist.CellPadding="13 8 8 8"
                        materialDesign:DataGridAssist.ColumnHeaderPadding="8">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="Daire No" Binding="{Binding FlatNo}" Width="Auto"/>
                        <DataGridTextColumn Header="Kira" Binding="{Binding RentAmount, StringFormat=C}" Width="Auto"/>
                        <DataGridTextColumn Header="Aidat" Binding="{Binding Dues, StringFormat=C}" Width="Auto"/>
                        <DataGridTextColumn Header="Kira Durumu" Binding="{Binding RentStatus}" Width="Auto"/>
                        <DataGridTextColumn Header="Aidat Durumu" Binding="{Binding DuesStatus}" Width="Auto"/>
                        <DataGridCheckBoxColumn Header="Dolu" Binding="{Binding IsOccupied}" Width="Auto"/>
                        <DataGridTextColumn Header="Oluşturma Tarihi" Binding="{Binding CreatedDate, StringFormat=dd.MM.yyyy}" Width="*"/>
                    </DataGrid.Columns>
                </DataGrid>

                <!-- Butonlar -->
                <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,16,0,0">
                    <Button Content="YENİ DAİRE"
                          Command="{Binding NewFlatCommand}"
                          Style="{DynamicResource MaterialDesignRaisedButton}"
                          Margin="0,0,8,0"/>
                    <Button Content="DÜZENLE"
                          Command="{Binding EditFlatCommand}"
                          Style="{DynamicResource MaterialDesignOutlinedButton}"
                          Margin="0,0,8,0"/>
                    <Button Content="SİL"
                          Command="{Binding DeleteFlatCommand}"
                          Style="{DynamicResource MaterialDesignOutlinedButton}"
                          Foreground="{DynamicResource ValidationErrorBrush}"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

            <!-- Daire Detay/Düzenleme Formu -->
            <materialDesign:Card Grid.Column="1">
            <Grid Margin="16" IsEnabled="{Binding SelectedFlat, Converter={StaticResource NotNullToBoolConverter}}">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Form Başlığı -->
                <TextBlock Grid.Row="0" Style="{DynamicResource MaterialDesignHeadline6TextBlock}" Margin="0,0,0,24"
                           Text="{Binding IsEditing, Converter={StaticResource BoolToStringConverter}, ConverterParameter='Daire Düzenle|Daire Detayları'}"/>

                <!-- Form Alanları -->
                <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- Genel Bilgiler -->
                        <StackPanel Grid.Row="0">
                            <TextBox materialDesign:HintAssist.Hint="Daire Numarası"
                                     Text="{Binding CurrentFlat.FlatNo}"
                                     IsReadOnly="{Binding IsEditing, Converter={StaticResource InverseBoolConverter}}"
                                     Style="{DynamicResource MaterialDesignFloatingHintTextBox}"
                                     Margin="0,8"/>
                        </StackPanel>

                        <!-- Finansal Bilgiler -->
                        <Grid Grid.Row="1" Margin="0,16,0,0">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBox Grid.Column="0" materialDesign:HintAssist.Hint="Kira Bedeli (₺)"
                                     Text="{Binding CurrentFlat.RentAmount}"
                                     IsReadOnly="{Binding IsEditing, Converter={StaticResource InverseBoolConverter}}"
                                     Style="{DynamicResource MaterialDesignFloatingHintTextBox}"
                                     Margin="0,8,4,8"/>
                            <TextBox Grid.Column="1" materialDesign:HintAssist.Hint="Aidat (₺)"
                                     Text="{Binding CurrentFlat.Dues}"
                                     IsReadOnly="{Binding IsEditing, Converter={StaticResource InverseBoolConverter}}"
                                     Style="{DynamicResource MaterialDesignFloatingHintTextBox}"
                                     Margin="4,8,0,8"/>
                        </Grid>

                        <!-- Durum Bilgileri -->
                        <Grid Grid.Row="2" Margin="0,16,0,0">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <ComboBox Grid.Column="0" materialDesign:HintAssist.Hint="Kira Durumu"
                                      SelectedValue="{Binding CurrentFlat.RentStatus}" IsEnabled="{Binding IsEditing}"
                                      Style="{DynamicResource MaterialDesignFloatingHintComboBox}" Margin="0,8,4,8">
                                <ComboBoxItem Content="Ödendi" Tag="Paid"/>
                                <ComboBoxItem Content="Ödenmedi" Tag="Due"/>
                            </ComboBox>
                            <ComboBox Grid.Column="1" materialDesign:HintAssist.Hint="Aidat Durumu"
                                      SelectedValue="{Binding CurrentFlat.DuesStatus}" IsEnabled="{Binding IsEditing}"
                                      Style="{DynamicResource MaterialDesignFloatingHintComboBox}" Margin="4,8,0,8">
                                <ComboBoxItem Content="Ödendi" Tag="Paid"/>
                                <ComboBoxItem Content="Ödenmedi" Tag="Due"/>
                            </ComboBox>
                        </Grid>

                        <!-- Kontrol Kutuları -->
                        <StackPanel Grid.Row="3" Margin="0,24,0,0">
                            <CheckBox Content="Daire Dolu" IsChecked="{Binding CurrentFlat.IsOccupied}" IsEnabled="{Binding IsEditing}" Style="{DynamicResource MaterialDesignCheckBox}" Margin="0,8"/>
                            <CheckBox Content="Aktif" IsChecked="{Binding CurrentFlat.IsActive}" IsEnabled="{Binding IsEditing}" Style="{DynamicResource MaterialDesignCheckBox}" Margin="0,8"/>
                        </StackPanel>
                    </Grid>
                </ScrollViewer>

                <!-- Form Butonları -->
                <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,16,0,0">
                    <Button Content="Düzenle" Command="{Binding EditFlatCommand}"
                            Visibility="{Binding IsEditing, Converter={StaticResource InverseBoolToVisibilityConverter}}">
                        <Button.Style>
                            <Style TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
                                <Setter Property="IsEnabled" Value="{Binding SelectedFlat, Converter={StaticResource NotNullToBoolConverter}}"/>
                            </Style>
                        </Button.Style>
                    </Button>
                    <Button Content="İptal" Command="{Binding CancelEditCommand}"
                            Visibility="{Binding IsEditing, Converter={StaticResource BoolToVisibilityConverter}}"
                            Style="{DynamicResource MaterialDesignOutlinedButton}"
                            Margin="8,0,0,0"/>
                    <Button Content="Kaydet" Command="{Binding SaveFlatCommand}"
                            Visibility="{Binding IsEditing, Converter={StaticResource BoolToVisibilityConverter}}"
                            Style="{DynamicResource MaterialDesignRaisedButton}"
                            Margin="8,0,0,0"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        </Grid>

        <!-- Loading Overlay -->
        <Grid Grid.RowSpan="2"
              Background="#80000000"
              Visibility="{Binding IsLoading, Converter={StaticResource BoolToVisibilityConverter}}">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <ProgressBar IsIndeterminate="True" Width="100" Height="100"
                           Style="{DynamicResource MaterialDesignCircularProgressBar}"/>
                <TextBlock Text="Yükleniyor..."
                         Foreground="White"
                         HorizontalAlignment="Center"
                         Margin="0,16,0,0"/>
            </StackPanel>
        </Grid>
    </Grid>
</UserControl>