using System.Windows.Controls;
using ApartmanYonetimSistemi.ViewModels;
using ApartmanYonetimSistemi.Models;

namespace ApartmanYonetimSistemi.Views
{
    /// <summary>
    /// Interaction logic for SimpleMainView.xaml
    /// </summary>
    public partial class SimpleMainView : UserControl
    {
        private MainViewModel? _viewModel;

        public SimpleMainView()
        {
            InitializeComponent();
            Loaded += OnLoaded;
        }

        private async void OnLoaded(object sender, System.Windows.RoutedEventArgs e)
        {
            _viewModel = DataContext as MainViewModel;
            if (_viewModel != null)
            {
                // Test kullanıcısı oluştur
                var testUser = new User
                {
                    Id = "test-user-1",
                    Name = "Admin Kullanıcı",
                    Email = "<EMAIL>",
                    Role = UserRoles.Admin
                };

                await _viewModel.InitializeAsync(testUser);

                // Logout event'ini dinle
                _viewModel.LogoutRequested += OnLogoutRequested;

                // Debug için başlangıç view'ını kontrol et
                System.Diagnostics.Debug.WriteLine($"Initial CurrentView: {_viewModel.CurrentView}");

                // PropertyChanged event'ini dinle
                _viewModel.PropertyChanged += (s, e) => {
                    if (e.PropertyName == "CurrentView")
                    {
                        System.Diagnostics.Debug.WriteLine($"CurrentView changed to: {_viewModel.CurrentView}");
                    }
                };
            }
        }

        private void OnLogoutRequested(object? sender, System.EventArgs e)
        {
            // Ana pencereyi kapat ve login ekranına dön
            var parentWindow = System.Windows.Window.GetWindow(this);
            if (parentWindow != null)
            {
                var loginWindow = new MainWindow();
                loginWindow.Show();
                parentWindow.Close();
            }
        }
    }
}
