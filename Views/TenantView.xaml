<UserControl x:Class="ApartmanYonetimSistemi.Views.TenantView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:vm="clr-namespace:ApartmanYonetimSistemi.ViewModels"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:converters="clr-namespace:ApartmanYonetimSistemi.Resources"
             xmlns:i="http://schemas.microsoft.com/xaml/behaviors"
             mc:Ignorable="d"
             d:DesignHeight="720" d:DesignWidth="1280">

    <i:Interaction.Triggers>
        <i:EventTrigger EventName="Loaded">
            <i:InvokeCommandAction Command="{Binding LoadTenantsCommand}"/>
        </i:EventTrigger>
    </i:Interaction.Triggers>

    <UserControl.Resources>
        <converters:StringToVisibilityConverter x:Key="StringToVisibilityConverter"/>
        <converters:BoolToVisibilityConverter x:Key="BoolToVisibilityConverter"/>
        <converters:InverseBoolToVisibilityConverter x:Key="InverseBoolToVisibilityConverter"/>
        <converters:BoolToStringConverter x:Key="BoolToStringConverter"/>
        <converters:NotNullToBoolConverter x:Key="NotNullToBoolConverter"/>
        <converters:InverseBoolConverter x:Key="InverseBoolConverter"/>
    </UserControl.Resources>



    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Modern Header -->
        <materialDesign:Card Grid.Row="0" Margin="0,0,0,16">
            <Grid Margin="24,16">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                <StackPanel Grid.Column="0" VerticalAlignment="Center">
                    <TextBlock Text="Kiracı Yönetimi" Style="{DynamicResource MaterialDesignHeadline5TextBlock}"/>
                    <TextBlock Text="Dairelere atanmış kiracıları yönetin" Style="{DynamicResource MaterialDesignBody2TextBlock}" Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                </StackPanel>
                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                    <Button Command="{Binding LoadTenantsCommand}" Style="{DynamicResource MaterialDesignOutlinedButton}" Margin="0,0,8,0" ToolTip="Yenile">
                        <materialDesign:PackIcon Kind="Refresh" />
                    </Button>
                    <Button Command="{Binding NewTenantCommand}" Style="{DynamicResource MaterialDesignRaisedButton}" ToolTip="Yeni Kiracı Ekle">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="AccountPlus" Margin="0,0,8,0"/>
                            <TextBlock Text="Yeni Kiracı"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Main Content -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="2*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Kiracı Listesi -->
            <materialDesign:Card Grid.Column="0" Margin="0,0,8,0">
            <Grid Margin="16">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Başlık -->
                <TextBlock Grid.Row="0" Text="Kiracılar" Style="{DynamicResource MaterialDesignHeadline6TextBlock}" Margin="0,0,0,16"/>

                <!-- Hata/Başarı Mesajları -->
                <StackPanel Grid.Row="1" Margin="0,0,0,16">
                    <materialDesign:Card Background="{DynamicResource ValidationErrorBrush}"
                                       Visibility="{Binding ErrorMessage, Converter={StaticResource StringToVisibilityConverter}}"
                                       Margin="0,0,0,8">
                        <TextBlock Text="{Binding ErrorMessage}"
                                 Foreground="White"
                                 Margin="16,8"
                                 TextWrapping="Wrap"/>
                    </materialDesign:Card>

                    <materialDesign:Card Background="{DynamicResource PrimaryHueMidBrush}"
                                       Visibility="{Binding SuccessMessage, Converter={StaticResource StringToVisibilityConverter}}"
                                       Margin="0,0,0,8">
                        <TextBlock Text="{Binding SuccessMessage}"
                                 Foreground="White"
                                 Margin="16,8"
                                 TextWrapping="Wrap"/>
                    </materialDesign:Card>
                </StackPanel>

                <!-- Kiracı Listesi -->
                <DataGrid Grid.Row="2"
                        ItemsSource="{Binding Tenants}"
                        SelectedItem="{Binding SelectedTenant, Mode=TwoWay}"
                        AutoGenerateColumns="False"
                        CanUserAddRows="False"
                        CanUserDeleteRows="False"
                        IsReadOnly="True"
                        materialDesign:DataGridAssist.CellPadding="13 8 8 8"
                        materialDesign:DataGridAssist.ColumnHeaderPadding="8">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="Ad Soyad" Binding="{Binding FullName}" Width="*"/>
                        <DataGridTextColumn Header="Telefon" Binding="{Binding Phone}" Width="Auto"/>
                        <DataGridTextColumn Header="E-posta" Binding="{Binding Email}" Width="*"/>
                        <DataGridTextColumn Header="Giriş Tarihi" Binding="{Binding EntryDate, StringFormat=dd.MM.yyyy}" Width="Auto"/>
                        <DataGridCheckBoxColumn Header="Aktif" Binding="{Binding IsActive}" Width="Auto"/>
                    </DataGrid.Columns>
                </DataGrid>

                <!-- Butonlar -->
                <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,16,0,0">
                    <Button Content="E-posta Gönder" Command="{Binding SendWelcomeEmailCommand}" Style="{DynamicResource MaterialDesignOutlinedButton}" Margin="0,0,8,0"/>
                    <Button Content="Sil" Command="{Binding DeleteTenantCommand}" Style="{DynamicResource MaterialDesignOutlinedButton}" Foreground="{DynamicResource ValidationErrorBrush}"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

            <!-- Kiracı Detay/Düzenleme Formu -->
            <materialDesign:Card Grid.Column="1">
            <Grid Margin="16" IsEnabled="{Binding SelectedTenant, Converter={StaticResource NotNullToBoolConverter}}">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Form Başlığı -->
                <TextBlock Grid.Row="0" Text="Kiracı Detayları" Style="{DynamicResource MaterialDesignHeadline6TextBlock}" Margin="0,0,0,24" />

                <!-- Form Alanları -->
                <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="0,0,10,0">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- Kişisel Bilgiler -->
                        <GroupBox Header="Kişisel Bilgiler" Style="{DynamicResource MaterialDesignCardGroupBox}" Margin="0,0,0,16">
                            <Grid>
                                <Grid.ColumnDefinitions><ColumnDefinition/><ColumnDefinition/></Grid.ColumnDefinitions>
                                <TextBox Grid.Column="0" materialDesign:HintAssist.Hint="Ad" Text="{Binding CurrentTenant.Name}" Style="{DynamicResource MaterialDesignFloatingHintTextBox}" IsReadOnly="{Binding IsEditing, Converter={StaticResource InverseBoolConverter}}" Margin="0,8,4,8"/>
                                <TextBox Grid.Column="1" materialDesign:HintAssist.Hint="Soyad" Text="{Binding CurrentTenant.Surname}" Style="{DynamicResource MaterialDesignFloatingHintTextBox}" IsReadOnly="{Binding IsEditing, Converter={StaticResource InverseBoolConverter}}" Margin="4,8,0,8"/>
                            </Grid>
                        </GroupBox>

                        <!-- İletişim Bilgileri -->
                        <GroupBox Grid.Row="1" Header="İletişim Bilgileri" Style="{DynamicResource MaterialDesignCardGroupBox}" Margin="0,0,0,16">
                            <Grid>
                                <Grid.RowDefinitions><RowDefinition/><RowDefinition/></Grid.RowDefinitions>
                                <TextBox Grid.Row="0" materialDesign:HintAssist.Hint="Telefon" Text="{Binding CurrentTenant.Phone}" Style="{DynamicResource MaterialDesignFloatingHintTextBox}" IsReadOnly="{Binding IsEditing, Converter={StaticResource InverseBoolConverter}}" Margin="0,8"/>
                                <TextBox Grid.Row="1" materialDesign:HintAssist.Hint="E-posta" Text="{Binding CurrentTenant.Email}" Style="{DynamicResource MaterialDesignFloatingHintTextBox}" IsReadOnly="{Binding IsEditing, Converter={StaticResource InverseBoolConverter}}" Margin="0,8"/>
                            </Grid>
                        </GroupBox>

                        <!-- Daire ve Sözleşme Bilgileri -->
                        <GroupBox Grid.Row="2" Header="Daire ve Sözleşme Bilgileri" Style="{DynamicResource MaterialDesignCardGroupBox}" Margin="0,0,0,16">
                            <StackPanel>
                                <ComboBox materialDesign:HintAssist.Hint="Daire" ItemsSource="{Binding AvailableFlats}" SelectedValue="{Binding CurrentTenant.FlatId}" SelectedValuePath="Id" DisplayMemberPath="FlatNo" Style="{DynamicResource MaterialDesignFloatingHintComboBox}" IsEnabled="{Binding IsEditing}" Margin="0,8"/>
                                <Grid>
                                    <Grid.ColumnDefinitions><ColumnDefinition/><ColumnDefinition/></Grid.ColumnDefinitions>
                                    <DatePicker Grid.Column="0" materialDesign:HintAssist.Hint="Giriş Tarihi" SelectedDate="{Binding CurrentTenant.EntryDate}" Style="{DynamicResource MaterialDesignFloatingHintDatePicker}" IsEnabled="{Binding IsEditing}" Margin="0,8,4,8"/>
                                    <DatePicker Grid.Column="1" materialDesign:HintAssist.Hint="Çıkış Tarihi" SelectedDate="{Binding CurrentTenant.ExitDate}" Style="{DynamicResource MaterialDesignFloatingHintDatePicker}" IsEnabled="{Binding IsEditing}" Margin="4,8,0,8"/>
                                </Grid>
                            </StackPanel>
                        </GroupBox>

                        <!-- Aktif Durumu -->
                        <CheckBox Grid.Row="4" Content="Aktif Kiracı" IsChecked="{Binding CurrentTenant.IsActive}" Style="{DynamicResource MaterialDesignCheckBox}" IsEnabled="{Binding IsEditing}" Margin="0,16,0,0"/>

                    </Grid>
                </ScrollViewer>

                <!-- Form Butonları -->
                <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,16,0,0">
                    <Button Content="Düzenle" Command="{Binding EditTenantCommand}"
                            Visibility="{Binding IsEditing, Converter={StaticResource InverseBoolToVisibilityConverter}}">
                        <Button.Style>
                            <Style TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
                                <Setter Property="IsEnabled" Value="{Binding SelectedTenant, Converter={StaticResource NotNullToBoolConverter}}"/>
                            </Style>
                        </Button.Style>
                    </Button>
                    <Button Content="İptal" Command="{Binding CancelEditCommand}"
                            Visibility="{Binding IsEditing, Converter={StaticResource BoolToVisibilityConverter}}"
                            Style="{DynamicResource MaterialDesignOutlinedButton}"
                            Margin="8,0,0,0"/>
                    <Button Content="Kaydet" Command="{Binding SaveTenantCommand}"
                            Visibility="{Binding IsEditing, Converter={StaticResource BoolToVisibilityConverter}}"
                            Style="{DynamicResource MaterialDesignRaisedButton}"
                            Margin="8,0,0,0"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        </Grid>

        <!-- Loading Overlay -->
        <Grid Grid.RowSpan="2"
              Background="#80000000"
              Visibility="{Binding IsLoading, Converter={StaticResource BoolToVisibilityConverter}}">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <ProgressBar IsIndeterminate="True" Width="100" Height="100"
                           Style="{DynamicResource MaterialDesignCircularProgressBar}"/>
                <TextBlock Text="Yükleniyor..."
                         Foreground="White"
                         HorizontalAlignment="Center"
                         Margin="0,16,0,0"/>
            </StackPanel>
        </Grid>
    </Grid>
</UserControl>