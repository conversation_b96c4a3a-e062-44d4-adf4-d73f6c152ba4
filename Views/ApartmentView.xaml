<UserControl x:Class="ApartmanYonetimSistemi.Views.ApartmentView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:vm="clr-namespace:ApartmanYonetimSistemi.ViewModels"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:converters="clr-namespace:ApartmanYonetimSistemi.Resources"
             xmlns:i="http://schemas.microsoft.com/xaml/behaviors"
             mc:Ignorable="d"
             d:DesignHeight="720" d:DesignWidth="1280">

    <i:Interaction.Triggers>
        <i:EventTrigger EventName="Loaded">
            <i:InvokeCommandAction Command="{Binding LoadApartmentsCommand}"/>
        </i:EventTrigger>
    </i:Interaction.Triggers>

    <UserControl.Resources>
        <converters:StringToVisibilityConverter x:Key="StringToVisibilityConverter"/>
        <converters:BoolToVisibilityConverter x:Key="BoolToVisibilityConverter"/>
        <converters:InverseBoolToVisibilityConverter x:Key="InverseBoolToVisibilityConverter"/>
        <converters:BoolToStringConverter x:Key="BoolToStringConverter"/>
        <converters:NotNullToBoolConverter x:Key="NotNullToBoolConverter"/>
    </UserControl.Resources>

    <UserControl.DataContext>
        <vm:ApartmentViewModel/>
    </UserControl.DataContext>

    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="2*"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- Modern Header -->
        <materialDesign:Card Grid.Row="0" Grid.ColumnSpan="2" Margin="0,0,0,16">
            <Grid Margin="24,16">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                <StackPanel Grid.Column="0" VerticalAlignment="Center">
                    <TextBlock Text="Apartman Yönetimi" Style="{DynamicResource MaterialDesignHeadline5TextBlock}"/>
                    <TextBlock Text="Seçili siteye ait apartmanları yönetin" Style="{DynamicResource MaterialDesignBody2TextBlock}" Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                </StackPanel>
                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                    <Button Command="{Binding LoadApartmentsCommand}" Style="{DynamicResource MaterialDesignOutlinedButton}" Margin="0,0,8,0" ToolTip="Yenile">
                        <materialDesign:PackIcon Kind="Refresh" />
                    </Button>
                    <Button Command="{Binding NewApartmentCommand}" Style="{DynamicResource MaterialDesignRaisedButton}" ToolTip="Yeni Apartman Ekle">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Plus" Margin="0,0,8,0"/>
                            <TextBlock Text="Yeni Apartman"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Apartman Listesi -->
        <materialDesign:Card Grid.Row="1" Grid.Column="0" Margin="0,0,8,0">
            <Grid Margin="16">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Başlık -->
                <TextBlock Grid.Row="0" Text="Apartmanlar" Style="{DynamicResource MaterialDesignHeadline6TextBlock}" Margin="0,0,0,16"/>

                <!-- Hata/Başarı Mesajları -->
                <StackPanel Grid.Row="1" Margin="0,0,0,16">
                    <materialDesign:Card Background="{DynamicResource ValidationErrorBrush}"
                                       Visibility="{Binding ErrorMessage, Converter={StaticResource StringToVisibilityConverter}}"
                                       Margin="0,0,0,8">
                        <TextBlock Text="{Binding ErrorMessage}"
                                 Foreground="White"
                                 Margin="16,8"
                                 TextWrapping="Wrap"/>
                    </materialDesign:Card>

                    <materialDesign:Card Background="{DynamicResource PrimaryHueMidBrush}"
                                       Visibility="{Binding SuccessMessage, Converter={StaticResource StringToVisibilityConverter}}"
                                       Margin="0,0,0,8">
                        <TextBlock Text="{Binding SuccessMessage}"
                                 Foreground="White"
                                 Margin="16,8"
                                 TextWrapping="Wrap"/>
                    </materialDesign:Card>
                </StackPanel>

                <!-- Apartman Listesi -->
                <DataGrid Grid.Row="2"
                        ItemsSource="{Binding Apartments}"
                        SelectedItem="{Binding SelectedApartment, Mode=TwoWay}"
                        AutoGenerateColumns="False"
                        CanUserAddRows="False"
                        CanUserDeleteRows="False"
                        IsReadOnly="True"
                        materialDesign:DataGridAssist.CellPadding="13 8 8 8"
                        materialDesign:DataGridAssist.ColumnHeaderPadding="8">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="Apartman Adı" Binding="{Binding ApartmentName}" Width="*"/>
                        <DataGridTextColumn Header="Adres" Binding="{Binding Address}" Width="2*"/>
                        <DataGridTextColumn Header="Toplam Daire" Binding="{Binding TotalFlats}" Width="Auto"/>
                        <DataGridTextColumn Header="Oluşturma Tarihi" Binding="{Binding CreatedDate, StringFormat=dd.MM.yyyy}" Width="Auto"/>
                    </DataGrid.Columns>
                </DataGrid>

                <!-- Butonlar -->
                <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,16,0,0">
                    <Button Content="YENİ APARTMAN"
                          Command="{Binding NewApartmentCommand}"
                          Style="{DynamicResource MaterialDesignRaisedButton}"
                          Margin="0,0,8,0"/>
                    <Button Content="DÜZENLE"
                          Command="{Binding EditApartmentCommand}"
                          Style="{DynamicResource MaterialDesignOutlinedButton}"
                          Margin="0,0,8,0"/>
                    <Button Content="SİL"
                          Command="{Binding DeleteApartmentCommand}"
                          Style="{DynamicResource MaterialDesignOutlinedButton}"
                          Foreground="{DynamicResource ValidationErrorBrush}"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Apartman Detay/Düzenleme Formu -->
        <materialDesign:Card Grid.Row="1" Grid.Column="1">
            <Grid Margin="16" IsEnabled="{Binding SelectedApartment, Converter={StaticResource NotNullToBoolConverter}}">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Form Başlığı -->
                <TextBlock Grid.Row="0" Style="{DynamicResource MaterialDesignHeadline6TextBlock}" Margin="0,0,0,24">
                    <TextBlock.Text>
                        <MultiBinding Converter="{StaticResource BoolToStringConverter}">
                            <Binding Path="IsEditing"/>
                            <Binding StringFormat="Apartman Düzenle"/>
                            <Binding StringFormat="Apartman Detayları"/>
                        </MultiBinding>
                    </TextBlock.Text>
                </TextBlock>

                <!-- Form Alanları -->
                <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <TextBox Grid.Row="0" materialDesign:HintAssist.Hint="Apartman Adı"
                                 Text="{Binding CurrentApartment.ApartmentName}"
                                 IsReadOnly="{Binding IsEditing, Converter={StaticResource InverseBoolConverter}}"
                                 Style="{DynamicResource MaterialDesignFloatingHintTextBox}"
                                 Margin="0,8"/>

                        <TextBox Grid.Row="1" materialDesign:HintAssist.Hint="Adres"
                                 Text="{Binding CurrentApartment.Address}"
                                 IsReadOnly="{Binding IsEditing, Converter={StaticResource InverseBoolConverter}}"
                                 Style="{DynamicResource MaterialDesignFloatingHintTextBox}"
                                 Margin="0,8"/>

                        <TextBox Grid.Row="2" materialDesign:HintAssist.Hint="Toplam Daire Sayısı"
                                 Text="{Binding CurrentApartment.TotalFlats}"
                                 IsReadOnly="{Binding IsEditing, Converter={StaticResource InverseBoolConverter}}"
                                 Style="{DynamicResource MaterialDesignFloatingHintTextBox}"
                                 Margin="0,8"/>

                        <CheckBox Grid.Row="3" Content="Aktif"
                                  IsChecked="{Binding CurrentApartment.IsActive}"
                                  IsEnabled="{Binding IsEditing}"
                                  Style="{DynamicResource MaterialDesignCheckBox}"
                                  Margin="0,16,0,0"/>
                    </Grid>
                </ScrollViewer>

                <!-- Form Butonları -->
                <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,16,0,0">
                    <Button Content="Düzenle" Command="{Binding EditApartmentCommand}"
                            Visibility="{Binding IsEditing, Converter={StaticResource InverseBoolToVisibilityConverter}}">
                        <Button.Style>
                            <Style TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
                                <Setter Property="IsEnabled" Value="{Binding SelectedApartment, Converter={StaticResource NotNullToBoolConverter}}"/>
                            </Style>
                        </Button.Style>
                    </Button>
                    <Button Content="İptal" Command="{Binding CancelEditCommand}"
                            Visibility="{Binding IsEditing, Converter={StaticResource BoolToVisibilityConverter}}"
                            Style="{DynamicResource MaterialDesignOutlinedButton}"
                            Margin="8,0,0,0"/>
                    <Button Content="Kaydet" Command="{Binding SaveApartmentCommand}"
                            Visibility="{Binding IsEditing, Converter={StaticResource BoolToVisibilityConverter}}"
                            Style="{DynamicResource MaterialDesignRaisedButton}"
                            Margin="8,0,0,0"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Loading Overlay -->
        <Grid Grid.ColumnSpan="2"
              Background="#80000000"
              Visibility="{Binding IsLoading, Converter={StaticResource BoolToVisibilityConverter}}">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <ProgressBar IsIndeterminate="True" Width="100" Height="100"
                           Style="{DynamicResource MaterialDesignCircularProgressBar}"/>
                <TextBlock Text="Yükleniyor..."
                         Foreground="White"
                         HorizontalAlignment="Center"
                         Margin="0,16,0,0"/>
            </StackPanel>
        </Grid>
    </Grid>
</UserControl>