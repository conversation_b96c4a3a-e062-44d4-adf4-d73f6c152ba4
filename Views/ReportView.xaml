<UserControl x:Class="ApartmanYonetimSistemi.Views.ReportView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:vm="clr-namespace:ApartmanYonetimSistemi.ViewModels"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:converters="clr-namespace:ApartmanYonetimSistemi.Resources"
             mc:Ignorable="d"
             d:DesignHeight="720" d:DesignWidth="1280">

    <UserControl.Resources>
        <converters:BoolToVisibilityConverter x:Key="BoolToVisibilityConverter"/>
        <converters:InverseBoolConverter x:Key="InverseBoolConverter"/>
    </UserControl.Resources>



    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <materialDesign:Card Grid.Row="0" Margin="0,0,0,16">
            <Grid Margin="24,16">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                <StackPanel Grid.Column="0" VerticalAlignment="Center">
                    <TextBlock Text="Raporlama" Style="{DynamicResource MaterialDesignHeadline5TextBlock}"/>
                    <TextBlock Text="Sistem verilerinden özel raporlar oluşturun" Style="{DynamicResource MaterialDesignBody2TextBlock}" Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Raporlama Seçenekleri ve Önizleme -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="320"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Rapor Filtreleri -->
            <materialDesign:Card Grid.Column="0" Margin="0,0,16,0">
                <StackPanel Margin="16">
                    <TextBlock Text="Rapor Seçenekleri" Style="{DynamicResource MaterialDesignHeadline6TextBlock}" Margin="0,0,0,24"/>

                    <ComboBox materialDesign:HintAssist.Hint="Rapor Türü"
                              ItemsSource="{Binding ReportTypes}"
                              SelectedItem="{Binding SelectedReportType}"
                              Style="{DynamicResource MaterialDesignFloatingHintComboBox}"
                              Margin="0,8"/>

                    <DatePicker materialDesign:HintAssist.Hint="Başlangıç Tarihi"
                                SelectedDate="{Binding StartDate}"
                                Style="{DynamicResource MaterialDesignFloatingHintDatePicker}"
                                Margin="0,16"/>

                    <DatePicker materialDesign:HintAssist.Hint="Bitiş Tarihi"
                                SelectedDate="{Binding EndDate}"
                                Style="{DynamicResource MaterialDesignFloatingHintDatePicker}"
                                Margin="0,16"/>
                    
                    <Button Content="Rapor Oluştur"
                            Command="{Binding GenerateReportCommand}"
                            Style="{DynamicResource MaterialDesignRaisedButton}"
                            Margin="0,32,0,0"
                            IsEnabled="{Binding IsLoading, Converter={StaticResource InverseBoolConverter}}"/>
                </StackPanel>
            </materialDesign:Card>

            <!-- Rapor Önizleme / Sonuçlar -->
            <materialDesign:Card Grid.Column="1">
                <Grid>
                    <!-- TODO: Rapor önizlemesi için bir DataGrid veya benzeri bir kontrol eklenebilir -->
                    <TextBlock Text="Rapor oluşturmak için soldaki seçenekleri kullanın." 
                               HorizontalAlignment="Center" 
                               VerticalAlignment="Center" 
                               Style="{DynamicResource MaterialDesignSubtitle1TextBlock}"
                               Foreground="{DynamicResource MaterialDesignBodyLight}"/>

                    <!-- Yükleniyor Ekranı -->
                    <Grid Background="#80FFFFFF" Visibility="{Binding IsLoading, Converter={StaticResource BoolToVisibilityConverter}}">
                        <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                            <ProgressBar IsIndeterminate="True" Width="50" Height="50"
                                       Style="{DynamicResource MaterialDesignCircularProgressBar}"/>
                            <TextBlock Text="Rapor oluşturuluyor..." Margin="0,16,0,0" Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                        </StackPanel>
                    </Grid>
                </Grid>
            </materialDesign:Card>
        </Grid>

    </Grid>
</UserControl> 