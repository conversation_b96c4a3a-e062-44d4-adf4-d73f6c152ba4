<Window x:Class="ApartmanYonetimSistemi.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:ApartmanYonetimSistemi"
        xmlns:views="clr-namespace:ApartmanYonetimSistemi.Views"
        mc:Ignorable="d"
        Title="Apartman Yönetim Sistemi"
        Height="700" Width="1200"
        WindowStartupLocation="CenterScreen">
    <Grid>
        <!-- Gradient Background -->
        <Grid.Background>
            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                <GradientStop Color="#667eea" Offset="0"/>
                <GradientStop Color="#764ba2" Offset="1"/>
            </LinearGradientBrush>
        </Grid.Background>

        <!-- Simple Login Card -->
        <Border Background="White"
                CornerRadius="10"
                Width="350" Height="400"
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                BorderBrush="#ddd" BorderThickness="1">

            <StackPanel Margin="30" VerticalAlignment="Center">
                <!-- Header -->
                <TextBlock Text="🏢 Apartman Yönetim Sistemi"
                           FontSize="20" FontWeight="Bold"
                           HorizontalAlignment="Center"
                           Foreground="#333" Margin="0,0,0,30"/>

                <!-- Email -->
                <TextBlock Text="E-posta:" FontWeight="Bold" Margin="0,0,0,5"/>
                <TextBox x:Name="EmailTextBox" Text="<EMAIL>"
                         Height="35" Padding="10" FontSize="14"
                         BorderBrush="#ccc" BorderThickness="1"
                         Margin="0,0,0,15"/>

                <!-- Password -->
                <TextBlock Text="Şifre:" FontWeight="Bold" Margin="0,0,0,5"/>
                <PasswordBox x:Name="PasswordBox"
                             Height="35" Padding="10" FontSize="14"
                             BorderBrush="#ccc" BorderThickness="1"
                             Margin="0,0,0,25"/>

                <!-- Login Button -->
                <Button x:Name="LoginButton"
                        Content="GİRİŞ YAP"
                        Click="LoginButton_Click"
                        Height="40" FontSize="16" FontWeight="Bold"
                        Background="#4CAF50" Foreground="White"
                        BorderThickness="0" Cursor="Hand"
                        Margin="0,0,0,20"/>

                <!-- Test Info -->
                <TextBlock Text="Test: <EMAIL> / 123456"
                           FontSize="11" Foreground="#666"
                           HorizontalAlignment="Center"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
