<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <!-- <PERSON><PERSON><PERSON><PERSON><PERSON>ili -->
    <Style x:Key="TitleTextStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="24"/>
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="Foreground" Value="#212121"/>
        <Setter Property="Margin" Value="0,0,0,16"/>
    </Style>
    <!-- Buton Stili -->
    <Style x:Key="PrimaryButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="#2196F3"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Padding" Value="12,6"/>
        <Setter Property="Margin" Value="4"/>
    </Style>
    <!-- TextBox Stili -->
    <Style x:Key="ModernTextBoxStyle" TargetType="TextBox">
        <Setter Property="Margin" Value="0,8"/>
        <Setter Property="Padding" Value="12,8"/>
    </Style>
    <!-- DataGrid Stili -->
    <Style x:Key="ModernDataGridStyle" TargetType="DataGrid">
        <Setter Property="Background" Value="White"/>
        <Setter Property="BorderBrush" Value="#E0E0E0"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="GridLinesVisibility" Value="Horizontal"/>
        <Setter Property="HorizontalGridLinesBrush" Value="#F5F5F5"/>
        <Setter Property="RowBackground" Value="White"/>
        <Setter Property="AlternatingRowBackground" Value="#FAFAFA"/>
        <Setter Property="HeadersVisibility" Value="Column"/>
        <Setter Property="AutoGenerateColumns" Value="False"/>
        <Setter Property="CanUserAddRows" Value="False"/>
        <Setter Property="CanUserDeleteRows" Value="False"/>
        <Setter Property="SelectionMode" Value="Single"/>
        <Setter Property="SelectionUnit" Value="FullRow"/>
    </Style>
</ResourceDictionary> 