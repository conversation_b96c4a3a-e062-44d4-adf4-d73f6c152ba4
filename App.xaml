<Application x:Class="ApartmanYonetimSistemi.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:ApartmanYonetimSistemi"
             xmlns:resources="clr-namespace:ApartmanYonetimSistemi.Resources"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- Material Design -->
                <materialDesign:BundledTheme BaseTheme="Light" PrimaryColor="DeepPurple" SecondaryColor="Lime" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!-- Global Converter'lar -->
            <resources:StringToVisibilityConverter x:Key="StringToVisibilityConverter"/>
            <resources:BoolToVisibilityConverter x:Key="BoolToVisibilityConverter"/>
            <resources:InverseBoolToVisibilityConverter x:Key="InverseBoolToVisibilityConverter"/>
            <resources:BoolToStringConverter x:Key="BoolToStringConverter"/>
            <resources:NotNullToBoolConverter x:Key="NotNullToBoolConverter"/>
            <resources:InverseBoolConverter x:Key="InverseBoolConverter"/>
        </ResourceDictionary>
    </Application.Resources>
</Application>
