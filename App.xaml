<Application x:Class="ApartmanYonetimSistemi.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:ApartmanYonetimSistemi"
             xmlns:resources="clr-namespace:ApartmanYonetimSistemi.Resources"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <materialDesign:BundledTheme BaseTheme="Light" PrimaryColor="Blue" SecondaryColor="LightBlue" />
            </ResourceDictionary.MergedDictionaries>

            <!-- Global Converter'lar -->
            <resources:StringToVisibilityConverter x:Key="StringToVisibilityConverter"/>
            <resources:BoolToVisibilityConverter x:Key="BoolToVisibilityConverter"/>
            <resources:InverseBoolToVisibilityConverter x:Key="InverseBoolToVisibilityConverter"/>
            <resources:BoolToStringConverter x:Key="BoolToStringConverter"/>
        </ResourceDictionary>
    </Application.Resources>
</Application>
